# 管理前端开发文档

## 🎛️ 管理端架构

### 设计原则
- **权限控制**: 所有操作需要管理员权限验证
- **实时监控**: 实时显示系统状态和用户活动
- **操作日志**: 记录所有管理员操作
- **响应式设计**: 支持PC和平板访问

### 技术栈
- **框架**: 原生JavaScript + 模块化设计
- **样式**: CSS3 + Bootstrap风格
- **图表**: Chart.js (可选)
- **表格**: 原生表格 + 分页组件

## 🏠 管理端首页

### HTML结构
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 彩票分析平台</title>
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>管理后台</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#dashboard" class="nav-link active">仪表盘</a></li>
                    <li><a href="#users" class="nav-link">用户管理</a></li>
                    <li><a href="#lottery" class="nav-link">彩种管理</a></li>
                    <li><a href="#online" class="nav-link">在线用户</a></li>
                    <li><a href="#logs" class="nav-link">操作日志</a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <header class="content-header">
                <h1 id="pageTitle">仪表盘</h1>
                <div class="header-actions">
                    <span class="admin-info">管理员：<span id="adminName"></span></span>
                    <button class="logout-btn" onclick="adminAuth.logout()">退出</button>
                </div>
            </header>

            <div class="content-body" id="contentBody">
                <!-- 动态内容区域 -->
            </div>
        </main>
    </div>

    <script src="js/admin-auth.js"></script>
    <script src="js/admin-router.js"></script>
    <script src="js/admin-components.js"></script>
    <script src="js/admin-main.js"></script>
</body>
</html>
```

### 管理端路由
```javascript
class AdminRouter {
  constructor() {
    this.routes = {
      'dashboard': () => this.loadDashboard(),
      'users': () => this.loadUserManagement(),
      'lottery': () => this.loadLotteryManagement(),
      'online': () => this.loadOnlineUsers(),
      'logs': () => this.loadOperationLogs()
    };
    
    this.currentRoute = 'dashboard';
  }

  init() {
    // 绑定导航点击事件
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const route = e.target.getAttribute('href').substring(1);
        this.navigate(route);
      });
    });

    // 加载默认页面
    this.navigate('dashboard');
  }

  navigate(route) {
    if (this.routes[route]) {
      // 更新导航状态
      document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
      });
      document.querySelector(`[href="#${route}"]`).classList.add('active');

      // 更新页面标题
      const titles = {
        'dashboard': '仪表盘',
        'users': '用户管理',
        'lottery': '彩种管理',
        'online': '在线用户',
        'logs': '操作日志'
      };
      document.getElementById('pageTitle').textContent = titles[route];

      // 加载页面内容
      this.routes[route]();
      this.currentRoute = route;
    }
  }

  async loadDashboard() {
    const content = `
      <div class="dashboard">
        <div class="stats-grid">
          <div class="stat-card">
            <h3>总用户数</h3>
            <div class="stat-number" id="totalUsers">-</div>
          </div>
          <div class="stat-card">
            <h3>在线用户</h3>
            <div class="stat-number" id="onlineUsers">-</div>
          </div>
          <div class="stat-card">
            <h3>活跃彩种</h3>
            <div class="stat-number" id="activeLotteries">-</div>
          </div>
          <div class="stat-card">
            <h3>今日采集</h3>
            <div class="stat-number" id="todayCollections">-</div>
          </div>
        </div>

        <div class="dashboard-content">
          <div class="collection-status">
            <h3>采集状态监控</h3>
            <div class="status-grid" id="collectionStatusGrid">
              <!-- 动态生成采集状态卡片 -->
            </div>
          </div>
        </div>
      </div>
    `;

    document.getElementById('contentBody').innerHTML = content;
    await this.loadDashboardData();
  }

  async loadDashboardData() {
    try {
      // 加载统计数据
      const statsResponse = await fetch('/api/admin/stats', {
        headers: adminAuth.getAuthHeaders()
      });
      const statsData = await statsResponse.json();

      if (statsData.success) {
        document.getElementById('totalUsers').textContent = statsData.data.totalUsers;
        document.getElementById('onlineUsers').textContent = statsData.data.onlineUsers;
        document.getElementById('activeLotteries').textContent = statsData.data.activeLotteries;
        document.getElementById('todayCollections').textContent = statsData.data.todayCollections;
      }

      // 加载采集状态
      const statusResponse = await fetch('/api/admin/collection-status', {
        headers: adminAuth.getAuthHeaders()
      });
      const statusData = await statusResponse.json();

      if (statusData.success) {
        this.renderCollectionStatus(statusData.data);
      }
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
    }
  }

  renderCollectionStatus(statusData) {
    const grid = document.getElementById('collectionStatusGrid');
    grid.innerHTML = '';

    Object.entries(statusData).forEach(([id, status]) => {
      const card = document.createElement('div');
      card.className = `status-card ${status.status?.toLowerCase() || 'normal'}`;
      card.innerHTML = `
        <h4>${status.name}</h4>
        <div class="status-info">
          <div class="info-item">
            <label>状态：</label>
            <span class="status-text">${this.getStatusText(status.status)}</span>
          </div>
          <div class="info-item">
            <label>采集间隔：</label>
            <span>${status.collectInterval || 5}秒</span>
          </div>
          <div class="info-item">
            <label>连续失败：</label>
            <span class="${status.consecutiveFailures > 0 ? 'error' : ''}">${status.consecutiveFailures || 0}次</span>
          </div>
          <div class="info-item">
            <label>最后成功：</label>
            <span>${status.lastSuccessTime ? new Date(status.lastSuccessTime).toLocaleString() : '--'}</span>
          </div>
        </div>
      `;
      grid.appendChild(card);
    });
  }

  getStatusText(status) {
    switch (status) {
      case 'ERROR': return '错误';
      case 'WARNING': return '警告';
      case 'NORMAL': return '正常';
      default: return '未知';
    }
  }
}
```

## 👥 用户管理

### 用户管理页面
```javascript
async loadUserManagement() {
  const content = `
    <div class="user-management">
      <div class="management-header">
        <button class="btn btn-primary" onclick="userManager.openCreateModal()">
          新增用户
        </button>
        <div class="search-box">
          <input type="text" id="userSearch" placeholder="搜索用户名或昵称">
          <button onclick="userManager.search()">搜索</button>
        </div>
      </div>

      <div class="user-table-container">
        <table class="admin-table">
          <thead>
            <tr>
              <th>用户名</th>
              <th>昵称</th>
              <th>状态</th>
              <th>会员权限</th>
              <th>注册时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="userTableBody">
            <!-- 动态生成用户列表 -->
          </tbody>
        </table>
      </div>

      <div class="pagination" id="userPagination">
        <!-- 分页组件 -->
      </div>
    </div>

    <!-- 用户编辑弹窗 -->
    <div class="modal" id="userModal" style="display: none;">
      <div class="modal-content">
        <h3 id="modalTitle">新增用户</h3>
        <form id="userForm">
          <div class="form-group">
            <label>用户名：</label>
            <input type="text" id="modalUsername" required>
          </div>
          
          <div class="form-group">
            <label>昵称：</label>
            <input type="text" id="modalNickname" required>
          </div>
          
          <div class="form-group">
            <label>密码：</label>
            <input type="password" id="modalPassword">
            <small>编辑时留空表示不修改密码</small>
          </div>
          
          <div class="form-group">
            <label>会员权限：</label>
            <div class="membership-list" id="membershipList">
              <!-- 动态生成彩种权限选择 -->
            </div>
          </div>
          
          <div class="form-actions">
            <button type="submit">保存</button>
            <button type="button" onclick="userManager.closeModal()">取消</button>
          </div>
        </form>
      </div>
    </div>
  `;

  document.getElementById('contentBody').innerHTML = content;
  
  // 初始化用户管理器
  if (!window.userManager) {
    window.userManager = new UserManager();
  }
  
  await userManager.init();
}

class UserManager {
  constructor() {
    this.users = [];
    this.currentPage = 1;
    this.pageSize = 20;
    this.currentEditId = null;
  }

  async init() {
    await this.loadUsers();
    await this.loadLotteryTypes();
    this.bindEvents();
  }

  async loadUsers(page = 1) {
    try {
      const response = await fetch(`/api/admin/users?page=${page}&limit=${this.pageSize}`, {
        headers: adminAuth.getAuthHeaders()
      });
      const data = await response.json();

      if (data.success) {
        this.users = data.data.users;
        this.renderUserTable();
        this.renderPagination(data.data.pagination);
      }
    } catch (error) {
      console.error('加载用户失败:', error);
    }
  }

  renderUserTable() {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';

    this.users.forEach(user => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${user.username}</td>
        <td>${user.nickname}</td>
        <td>
          <span class="status-badge ${user.status === 'active' ? 'success' : 'danger'}">
            ${user.status === 'active' ? '正常' : '禁用'}
          </span>
        </td>
        <td>
          <div class="membership-tags">
            ${user.memberships.map(m => 
              `<span class="membership-tag ${m.status}">${m.lotteryTypeName}</span>`
            ).join('')}
          </div>
        </td>
        <td>${new Date(user.createdAt).toLocaleDateString()}</td>
        <td>
          <button class="btn-sm btn-edit" onclick="userManager.editUser(${user.id})">编辑</button>
          <button class="btn-sm btn-danger" onclick="userManager.deleteUser(${user.id})">删除</button>
        </td>
      `;
      tbody.appendChild(row);
    });
  }

  async createUser(userData) {
    try {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...adminAuth.getAuthHeaders()
        },
        body: JSON.stringify(userData)
      });

      const data = await response.json();

      if (data.success) {
        alert('用户创建成功');
        this.closeModal();
        await this.loadUsers();
      } else {
        alert('创建失败: ' + data.message);
      }
    } catch (error) {
      alert('创建失败: ' + error.message);
    }
  }

  openCreateModal() {
    document.getElementById('modalTitle').textContent = '新增用户';
    document.getElementById('userForm').reset();
    this.currentEditId = null;
    document.getElementById('userModal').style.display = 'block';
  }

  closeModal() {
    document.getElementById('userModal').style.display = 'none';
    this.currentEditId = null;
  }

  bindEvents() {
    document.getElementById('userForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const formData = new FormData(e.target);
      const userData = Object.fromEntries(formData);
      
      if (this.currentEditId) {
        await this.updateUser(this.currentEditId, userData);
      } else {
        await this.createUser(userData);
      }
    });
  }
}
```

## 🎲 彩种管理

### 彩种管理页面
```javascript
async loadLotteryManagement() {
  const content = `
    <div class="lottery-management">
      <div class="lottery-list">
        <table class="admin-table">
          <thead>
            <tr>
              <th>彩种名称</th>
              <th>彩种类型</th>
              <th>开奖间隔</th>
              <th>采集间隔</th>
              <th>采集状态</th>
              <th>最后采集</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="lotteryTableBody">
            <!-- 动态生成彩种列表 -->
          </tbody>
        </table>
      </div>

      <!-- 采集配置弹窗 -->
      <div class="modal" id="collectConfigModal" style="display: none;">
        <div class="modal-content">
          <h3>采集配置</h3>
          <form id="collectConfigForm">
            <div class="form-group">
              <label>彩种名称：</label>
              <span id="modalLotteryName"></span>
            </div>
            
            <div class="form-group">
              <label>采集间隔（秒）：</label>
              <input type="number" id="collectInterval" min="1" max="3600" required>
              <small>建议：高频彩5秒，传统彩30秒</small>
            </div>
            
            <div class="form-group">
              <label>数据源URL：</label>
              <input type="url" id="dataSourceUrl" required>
            </div>
            
            <div class="form-actions">
              <button type="submit">保存配置</button>
              <button type="button" onclick="lotteryManager.closeConfigModal()">取消</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `;

  document.getElementById('contentBody').innerHTML = content;
  
  if (!window.lotteryManager) {
    window.lotteryManager = new LotteryManager();
  }
  
  await lotteryManager.init();
}

class LotteryManager {
  constructor() {
    this.lotteryTypes = [];
    this.collectionStatus = {};
    this.currentEditId = null;
  }

  async init() {
    await this.loadLotteryTypes();
    await this.loadCollectionStatus();
    this.bindEvents();
    this.startStatusMonitor();
  }

  async loadLotteryTypes() {
    try {
      const response = await fetch('/api/admin/lottery-types', {
        headers: adminAuth.getAuthHeaders()
      });
      const data = await response.json();

      if (data.success) {
        this.lotteryTypes = data.data;
        this.renderLotteryTable();
      }
    } catch (error) {
      console.error('加载彩种失败:', error);
    }
  }

  renderLotteryTable() {
    const tbody = document.getElementById('lotteryTableBody');
    tbody.innerHTML = '';

    this.lotteryTypes.forEach(lottery => {
      const status = this.collectionStatus[lottery.id] || {};
      
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${lottery.name}</td>
        <td>${lottery.type}</td>
        <td>${lottery.drawInterval ? lottery.drawInterval + '秒' : '每日一期'}</td>
        <td>${lottery.collectInterval || 5}秒</td>
        <td>
          <span class="status-badge ${this.getStatusClass(status.status)}">
            ${this.getStatusText(status.status)}
          </span>
        </td>
        <td>${status.lastSuccessTime ? new Date(status.lastSuccessTime).toLocaleString() : '--'}</td>
        <td>
          <label class="switch">
            <input type="checkbox" ${lottery.status === 'active' ? 'checked' : ''} 
                   onchange="lotteryManager.toggleStatus(${lottery.id}, this.checked)">
            <span class="slider"></span>
          </label>
        </td>
        <td>
          <button class="btn-sm btn-config" onclick="lotteryManager.openConfigModal(${lottery.id})">
            配置
          </button>
          <button class="btn-sm btn-collect" onclick="lotteryManager.manualCollect(${lottery.id})">
            采集
          </button>
        </td>
      `;
      
      tbody.appendChild(row);
    });
  }

  async manualCollect(lotteryId) {
    try {
      const response = await fetch(`/api/admin/lottery-types/${lotteryId}/collect`, {
        method: 'POST',
        headers: adminAuth.getAuthHeaders()
      });

      const data = await response.json();

      if (data.success) {
        alert('采集任务已触发');
        setTimeout(() => {
          this.loadCollectionStatus();
        }, 3000);
      } else {
        alert('触发失败: ' + data.message);
      }
    } catch (error) {
      alert('触发失败: ' + error.message);
    }
  }

  startStatusMonitor() {
    setInterval(() => {
      this.loadCollectionStatus();
    }, 30000);
  }

  getStatusClass(status) {
    switch (status) {
      case 'ERROR': return 'error';
      case 'WARNING': return 'warning';
      case 'NORMAL': return 'success';
      default: return 'unknown';
    }
  }

  getStatusText(status) {
    switch (status) {
      case 'ERROR': return '错误';
      case 'WARNING': return '警告';
      case 'NORMAL': return '正常';
      default: return '未知';
    }
  }
}
```

### 4.3 彩种数据管理API

#### 4.3.1 支持的彩种代码
```javascript
const LOTTERY_CODES = {
  FC3D: '福彩3D',
  PL3: '排列三',
  PL4: '排列四',
  PC28: '加拿大PC28',
  XYWXC: '幸运五星彩前四'
};
```

#### 4.3.2 数据重置功能
**用途**: 清空指定彩种的所有历史数据
```javascript
// API调用示例
const resetLotteryData = async (lotteryCode) => {
  try {
    const response = await fetch(`/api/collection/data/${lotteryCode}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${adminAuth.token}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    if (result.success) {
      console.log(`${lotteryCode} 数据重置成功`);
      // 刷新数据统计
      await this.refreshDataStats();
      alert(`${LOTTERY_CODES[lotteryCode]} 数据重置成功`);
    } else {
      alert(`重置失败: ${result.message}`);
    }
  } catch (error) {
    console.error('数据重置失败:', error);
    alert('数据重置失败，请重试');
  }
};

// 使用示例
resetLotteryData('FC3D');  // 重置福彩3D数据
resetLotteryData('PC28');  // 重置PC28数据
```

#### 4.3.3 手动采集功能
**用途**: 立即触发指定彩种的数据采集
```javascript
// API调用示例
const manualCollect = async (lotteryCode) => {
  try {
    // 显示加载状态
    const loadingMsg = `正在采集 ${LOTTERY_CODES[lotteryCode]} 数据...`;
    this.showLoading(loadingMsg);

    const response = await fetch(`/api/collection/manual/${lotteryCode}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminAuth.token}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    if (result.success) {
      console.log(`${lotteryCode} 手动采集成功`);
      // 显示采集结果
      this.showCollectionResult(result);
      alert(`${LOTTERY_CODES[lotteryCode]} 采集成功`);
    } else {
      alert(`采集失败: ${result.message}`);
    }
  } catch (error) {
    console.error('手动采集失败:', error);
    alert('手动采集失败，请重试');
  } finally {
    this.hideLoading();
  }
};

// 使用示例
manualCollect('XYWXC');  // 手动采集幸运五星彩
manualCollect('PL3');    // 手动采集排列三
```

#### 4.3.4 历史数据初始化
**用途**: 批量采集指定彩种的历史数据（适用于新彩种或数据重置后）
```javascript
// API调用示例
const initHistoricalData = async (lotteryCode) => {
  try {
    // 确认操作
    const confirmed = confirm(`确定要初始化 ${LOTTERY_CODES[lotteryCode]} 的历史数据吗？\n这可能需要几分钟时间。`);
    if (!confirmed) return;

    // 显示加载状态
    this.showLoading(`正在初始化 ${LOTTERY_CODES[lotteryCode]} 历史数据，请耐心等待...`);

    const response = await fetch(`/api/collection/init/${lotteryCode}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminAuth.token}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    if (result.success) {
      console.log(`${lotteryCode} 历史数据初始化成功`);
      // 刷新数据统计
      await this.refreshDataStats();
      alert(`${LOTTERY_CODES[lotteryCode]} 历史数据初始化成功`);
    } else {
      alert(`初始化失败: ${result.message}`);
    }
  } catch (error) {
    console.error('历史数据初始化失败:', error);
    alert('历史数据初始化失败，请重试');
  } finally {
    this.hideLoading();
  }
};

// 使用示例
initHistoricalData('PC28');   // 初始化PC28历史数据
initHistoricalData('FC3D');   // 初始化福彩3D历史数据
```

#### 4.3.5 数据统计查询
```javascript
// 获取所有彩种数据统计
const getDataStats = async () => {
  try {
    const response = await fetch('/api/collection/stats', {
      headers: {
        'Authorization': `Bearer ${adminAuth.token}`
      }
    });

    const result = await response.json();
    if (result.success) {
      return result.data;
    }
    return [];
  } catch (error) {
    console.error('获取数据统计失败:', error);
    return [];
  }
};

// 数据统计显示格式
const displayDataStats = (stats) => {
  const statsContainer = document.getElementById('dataStatsContainer');
  statsContainer.innerHTML = '';

  stats.forEach(item => {
    const statCard = document.createElement('div');
    statCard.className = 'stat-card';
    statCard.innerHTML = `
      <h4>${item.name}</h4>
      <div class="stat-details">
        <div class="stat-item">
          <label>数据量:</label>
          <span class="stat-value">${item.count.toLocaleString()}条</span>
        </div>
        <div class="stat-item">
          <label>最新开奖:</label>
          <span class="stat-time">${item.latestTime || '--'}</span>
        </div>
        <div class="stat-item">
          <label>最新期号:</label>
          <span class="stat-period">${item.latestPeriod || '--'}</span>
        </div>
      </div>
    `;
    statsContainer.appendChild(statCard);
  });
};
```

#### 4.3.6 批量操作示例
```javascript
// 批量重置所有彩种数据
const resetAllData = async () => {
  const codes = ['FC3D', 'PL3', 'PL4', 'PC28', 'XYWXC'];
  const confirmed = confirm('确定要重置所有彩种数据吗？此操作不可恢复！');

  if (!confirmed) return;

  this.showLoading('正在重置所有彩种数据...');

  try {
    for (const code of codes) {
      console.log(`正在重置 ${LOTTERY_CODES[code]}...`);
      await resetLotteryData(code);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 间隔1秒
    }
    alert('所有彩种数据重置完成');
  } catch (error) {
    alert('批量重置过程中出现错误，请检查日志');
  } finally {
    this.hideLoading();
  }
};

// 批量初始化所有彩种历史数据
const initAllHistoricalData = async () => {
  const codes = ['FC3D', 'PL3', 'PL4', 'PC28', 'XYWXC'];
  const confirmed = confirm('确定要初始化所有彩种的历史数据吗？\n这可能需要10-15分钟时间。');

  if (!confirmed) return;

  this.showLoading('正在批量初始化历史数据，请耐心等待...');

  try {
    for (const code of codes) {
      console.log(`正在初始化 ${LOTTERY_CODES[code]}...`);
      await initHistoricalData(code);
      await new Promise(resolve => setTimeout(resolve, 2000)); // 间隔2秒
    }
    alert('所有彩种历史数据初始化完成');
  } catch (error) {
    alert('批量初始化过程中出现错误，请检查日志');
  } finally {
    this.hideLoading();
  }
};

// 批量手动采集所有彩种
const collectAllData = async () => {
  const codes = ['FC3D', 'PL3', 'PL4', 'PC28', 'XYWXC'];

  this.showLoading('正在采集所有彩种数据...');

  try {
    for (const code of codes) {
      console.log(`正在采集 ${LOTTERY_CODES[code]}...`);
      await manualCollect(code);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 间隔1秒
    }
    alert('所有彩种数据采集完成');
  } catch (error) {
    alert('批量采集过程中出现错误，请检查日志');
  } finally {
    this.hideLoading();
  }
};
```

#### 4.3.7 采集服务控制
```javascript
// 启动采集服务
const startCollectionService = async () => {
  try {
    const response = await fetch('/api/collection/start', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminAuth.token}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    if (result.success) {
      alert('采集服务启动成功');
      this.updateServiceStatus('running');
    } else {
      alert(`启动失败: ${result.message}`);
    }
  } catch (error) {
    console.error('启动采集服务失败:', error);
    alert('启动采集服务失败，请重试');
  }
};

// 停止采集服务
const stopCollectionService = async () => {
  try {
    const confirmed = confirm('确定要停止采集服务吗？');
    if (!confirmed) return;

    const response = await fetch('/api/collection/stop', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminAuth.token}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    if (result.success) {
      alert('采集服务已停止');
      this.updateServiceStatus('stopped');
    } else {
      alert(`停止失败: ${result.message}`);
    }
  } catch (error) {
    console.error('停止采集服务失败:', error);
    alert('停止采集服务失败，请重试');
  }
};

// 获取采集服务状态
const getCollectionServiceStatus = async () => {
  try {
    const response = await fetch('/api/collection/status', {
      headers: {
        'Authorization': `Bearer ${adminAuth.token}`
      }
    });

    const result = await response.json();
    if (result.success) {
      return result.data;
    }
    return { status: 'unknown' };
  } catch (error) {
    console.error('获取服务状态失败:', error);
    return { status: 'error' };
  }
};
```

#### 4.3.8 错误处理和状态管理
```javascript
// 统一错误处理
const handleApiError = (error, operation) => {
  console.error(`${operation}失败:`, error);

  // 显示用户友好的错误信息
  const errorMessages = {
    404: '彩种不存在或未启用',
    500: '服务器内部错误',
    timeout: '请求超时，请稍后重试',
    network: '网络连接失败'
  };

  const message = errorMessages[error.status] || errorMessages[error.type] || '操作失败，请重试';
  this.showErrorMessage(message);
};

// 操作状态跟踪
const trackOperation = (lotteryCode, operation) => {
  const operationId = `${lotteryCode}_${operation}_${Date.now()}`;

  // 记录操作开始
  console.log(`开始执行: ${operation} - ${LOTTERY_CODES[lotteryCode]}`);

  return {
    success: (result) => {
      console.log(`操作成功: ${operation} - ${LOTTERY_CODES[lotteryCode]}`, result);
      this.logOperation(lotteryCode, operation, 'success', result);
    },
    error: (err) => {
      console.error(`操作失败: ${operation} - ${LOTTERY_CODES[lotteryCode]}`, err);
      this.logOperation(lotteryCode, operation, 'error', err);
    }
  };
};

// UI辅助函数
const UIHelpers = {
  // 显示加载状态
  showLoading: (message = '正在处理...') => {
    const loadingEl = document.getElementById('loadingOverlay') || document.createElement('div');
    loadingEl.id = 'loadingOverlay';
    loadingEl.className = 'loading-overlay';
    loadingEl.innerHTML = `
      <div class="loading-content">
        <div class="spinner"></div>
        <div class="loading-text">${message}</div>
      </div>
    `;
    document.body.appendChild(loadingEl);
  },

  // 隐藏加载状态
  hideLoading: () => {
    const loadingEl = document.getElementById('loadingOverlay');
    if (loadingEl) {
      loadingEl.remove();
    }
  },

  // 显示成功消息
  showSuccessMessage: (message) => {
    const toast = document.createElement('div');
    toast.className = 'toast toast-success';
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
      toast.classList.add('show');
    }, 100);

    setTimeout(() => {
      toast.remove();
    }, 3000);
  },

  // 显示错误消息
  showErrorMessage: (message) => {
    const toast = document.createElement('div');
    toast.className = 'toast toast-error';
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
      toast.classList.add('show');
    }, 100);

    setTimeout(() => {
      toast.remove();
    }, 5000);
  },

  // 确认对话框
  confirmDialog: (message, onConfirm, onCancel) => {
    const modal = document.createElement('div');
    modal.className = 'confirm-modal';
    modal.innerHTML = `
      <div class="confirm-content">
        <h3>确认操作</h3>
        <p>${message}</p>
        <div class="confirm-actions">
          <button class="btn btn-primary" id="confirmYes">确定</button>
          <button class="btn btn-secondary" id="confirmNo">取消</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    document.getElementById('confirmYes').onclick = () => {
      modal.remove();
      if (onConfirm) onConfirm();
    };

    document.getElementById('confirmNo').onclick = () => {
      modal.remove();
      if (onCancel) onCancel();
    };
  }
};
```

#### 4.3.9 完整的彩种管理组件示例
```javascript
class LotteryDataManager {
  constructor() {
    this.lotteryTypes = [];
    this.dataStats = [];
    this.serviceStatus = 'unknown';
  }

  async init() {
    await this.loadLotteryTypes();
    await this.loadDataStats();
    await this.loadServiceStatus();
    this.bindEvents();
    this.startAutoRefresh();
  }

  // 渲染彩种管理界面
  render() {
    const container = document.getElementById('lotteryManagementContainer');
    container.innerHTML = `
      <div class="lottery-data-management">
        <!-- 服务控制区 -->
        <div class="service-control">
          <h3>采集服务控制</h3>
          <div class="service-status">
            <span class="status-indicator ${this.serviceStatus}"></span>
            <span class="status-text">${this.getServiceStatusText()}</span>
          </div>
          <div class="service-actions">
            <button class="btn btn-success" onclick="lotteryDataManager.startService()">启动服务</button>
            <button class="btn btn-danger" onclick="lotteryDataManager.stopService()">停止服务</button>
            <button class="btn btn-info" onclick="lotteryDataManager.refreshStatus()">刷新状态</button>
          </div>
        </div>

        <!-- 数据统计区 -->
        <div class="data-stats-section">
          <h3>数据统计</h3>
          <div class="stats-grid" id="dataStatsContainer">
            <!-- 动态生成统计卡片 -->
          </div>
        </div>

        <!-- 彩种操作区 -->
        <div class="lottery-operations">
          <h3>彩种数据管理</h3>
          <div class="operation-buttons">
            <button class="btn btn-warning" onclick="lotteryDataManager.resetAllData()">重置所有数据</button>
            <button class="btn btn-primary" onclick="lotteryDataManager.initAllData()">初始化所有数据</button>
            <button class="btn btn-info" onclick="lotteryDataManager.collectAllData()">采集所有数据</button>
          </div>

          <div class="lottery-grid">
            ${Object.entries(LOTTERY_CODES).map(([code, name]) => `
              <div class="lottery-card" data-code="${code}">
                <h4>${name}</h4>
                <div class="lottery-stats">
                  <div class="stat-item">
                    <label>数据量:</label>
                    <span class="data-count" id="count-${code}">-</span>
                  </div>
                  <div class="stat-item">
                    <label>最新:</label>
                    <span class="latest-time" id="time-${code}">-</span>
                  </div>
                </div>
                <div class="lottery-actions">
                  <button class="btn-sm btn-danger" onclick="lotteryDataManager.resetData('${code}')">重置</button>
                  <button class="btn-sm btn-primary" onclick="lotteryDataManager.initData('${code}')">初始化</button>
                  <button class="btn-sm btn-info" onclick="lotteryDataManager.collectData('${code}')">采集</button>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;

    this.updateStatsDisplay();
  }

  // 更新统计显示
  updateStatsDisplay() {
    Object.entries(LOTTERY_CODES).forEach(([code, name]) => {
      const stats = this.dataStats.find(s => s.code === code);
      if (stats) {
        const countEl = document.getElementById(`count-${code}`);
        const timeEl = document.getElementById(`time-${code}`);

        if (countEl) countEl.textContent = stats.count.toLocaleString() + '条';
        if (timeEl) timeEl.textContent = stats.latestTime ?
          new Date(stats.latestTime).toLocaleString() : '--';
      }
    });
  }

  // 自动刷新
  startAutoRefresh() {
    setInterval(async () => {
      await this.loadDataStats();
      await this.loadServiceStatus();
      this.updateStatsDisplay();
    }, 30000); // 每30秒刷新一次
  }

  // 包装API调用方法
  async resetData(code) { return await resetLotteryData(code); }
  async initData(code) { return await initHistoricalData(code); }
  async collectData(code) { return await manualCollect(code); }
  async resetAllData() { return await resetAllData(); }
  async initAllData() { return await initAllHistoricalData(); }
  async collectAllData() { return await collectAllData(); }
  async startService() { return await startCollectionService(); }
  async stopService() { return await stopCollectionService(); }
}

// 全局实例
const lotteryDataManager = new LotteryDataManager();
```

#### 4.3.10 彩种管理相关CSS样式
```css
/* 彩种数据管理样式 */
.lottery-data-management {
  padding: 20px;
}

.service-control {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.service-status {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.running { background: #28a745; }
.status-indicator.stopped { background: #dc3545; }
.status-indicator.unknown { background: #6c757d; }

.lottery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.lottery-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid #007bff;
}

.lottery-card h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.lottery-stats {
  margin-bottom: 15px;
}

.lottery-stats .stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.lottery-stats label {
  font-weight: 500;
  color: #666;
}

.lottery-actions {
  display: flex;
  gap: 8px;
}

.lottery-actions .btn-sm {
  flex: 1;
  padding: 6px 12px;
  font-size: 12px;
}

/* 加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 消息提示 */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.toast.show {
  transform: translateX(0);
}

.toast-success {
  background: #28a745;
}

.toast-error {
  background: #dc3545;
}

/* 确认对话框 */
.confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  max-width: 400px;
  text-align: center;
}

.confirm-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
}
```

#### 4.3.11 使用说明和注意事项

##### 🔧 **API调用顺序建议**
1. **新彩种配置**: 数据重置 → 历史数据初始化 → 启动采集服务
2. **日常维护**: 手动采集 → 查看统计 → 检查服务状态
3. **故障恢复**: 停止服务 → 数据重置 → 重新初始化 → 启动服务

##### ⚠️ **重要注意事项**
- **数据重置**: 会永久删除所有历史数据，操作前请确认
- **历史初始化**:
  - 传统彩种(FC3D/PL3/PL4): 约1-2分钟
  - 高频彩种(PC28/XYWXC): 约3-5分钟
- **批量操作**: 建议在低峰期执行，避免影响用户体验
- **服务控制**: 停止服务会影响实时数据更新

##### 📊 **数据量参考标准**
- **福彩3D**: 8000+条 (20年历史数据)
- **排列三/四**: 7000+条 (20年历史数据)
- **PC28**: 2000+条 (5天历史数据)
- **幸运五星彩**: 2000+条 (7天历史数据)

##### 🚨 **错误处理**
```javascript
// 统一错误处理示例
try {
  await lotteryDataManager.resetData('FC3D');
} catch (error) {
  if (error.status === 404) {
    alert('彩种不存在，请检查配置');
  } else if (error.status === 500) {
    alert('服务器错误，请联系技术支持');
  } else {
    alert('操作失败，请重试');
  }
}
```

##### 📝 **操作日志记录**
所有管理操作都会自动记录到系统日志中，包括：
- 操作时间和管理员
- 操作类型和目标彩种
- 操作结果和错误信息
- 数据变化统计

## 🔐 管理员认证

### 认证管理器
```javascript
class AdminAuth {
  constructor() {
    this.token = localStorage.getItem('adminToken');
    this.admin = null;
  }

  async login(username, password) {
    try {
      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();

      if (data.success) {
        this.token = data.data.token;
        this.admin = data.data.admin;
        
        localStorage.setItem('adminToken', this.token);
        localStorage.setItem('admin', JSON.stringify(this.admin));
        
        return { success: true };
      } else {
        return { success: false, message: data.message };
      }
    } catch (error) {
      return { success: false, message: '登录失败，请重试' };
    }
  }

  async logout() {
    try {
      await fetch('/api/admin/auth/logout', {
        method: 'POST',
        headers: this.getAuthHeaders()
      });
    } catch (error) {
      console.log('登出请求失败');
    }

    this.token = null;
    this.admin = null;
    localStorage.removeItem('adminToken');
    localStorage.removeItem('admin');
    
    window.location.href = '/admin/login.html';
  }

  getAuthHeaders() {
    return this.token ? { 'Authorization': `Bearer ${this.token}` } : {};
  }

  async checkAuth() {
    if (!this.token) {
      window.location.href = '/admin/login.html';
      return false;
    }

    try {
      const response = await fetch('/api/admin/auth/verify', {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('认证失败');
      }

      return true;
    } catch (error) {
      this.logout();
      return false;
    }
  }
}

// 全局管理员认证
const adminAuth = new AdminAuth();
```

## 🎨 管理端样式

### CSS样式
```css
/* 管理端布局 */
.admin-layout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #34495e;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
}

.sidebar-nav .nav-link {
  display: block;
  padding: 15px 20px;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
  background: #34495e;
  color: white;
}

.main-content {
  flex: 1;
  background: #f8f9fa;
}

.content-header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-body {
  padding: 20px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #007bff;
  margin-top: 10px;
}

/* 表格样式 */
.admin-table {
  width: 100%;
  background: white;
  border-collapse: collapse;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-table th,
.admin-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.admin-table th {
  background: #f8f9fa;
  font-weight: 600;
}

/* 状态徽章 */
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.success {
  background: #d4edda;
  color: #155724;
}

.status-badge.error {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.warning {
  background: #fff3cd;
  color: #856404;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-edit {
  background: #28a745;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
```

---

## 📋 更新日志

### v4.0 (2025-07-06)
- ✅ **新增**: 完整的彩种数据管理API文档
- ✅ **新增**: 5个彩种的数据重置、手动采集、历史初始化功能
- ✅ **新增**: 批量操作和服务控制功能
- ✅ **新增**: 错误处理和状态管理机制
- ✅ **新增**: UI辅助函数和完整的CSS样式
- ✅ **新增**: 使用说明和注意事项

### v3.0 (2025-07-05)
- ✅ **新增**: 基础管理端架构和路由系统
- ✅ **新增**: 用户管理和彩种管理页面
- ✅ **新增**: 管理员认证和权限控制

---

**📝 文档版本**: v4.0
**📅 更新时间**: 2025-07-06
**👨‍💻 维护者**: 开发团队

**🎯 支持的彩种**: 福彩3D、排列三、排列四、加拿大PC28、幸运五星彩前四
**🔧 支持的功能**: 数据重置、手动采集、历史初始化、批量操作、服务控制
