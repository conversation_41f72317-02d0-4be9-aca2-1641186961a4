"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const DataCollectionService_1 = require("../services/DataCollectionService");
const router = (0, express_1.Router)();
const collectionService = DataCollectionService_1.DataCollectionService.getInstance();
router.get('/status', (req, res) => {
    try {
        const status = collectionService.getStatus();
        res.json({
            success: true,
            data: status
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: '获取采集状态失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
router.post('/start', async (req, res) => {
    try {
        await collectionService.start();
        res.json({
            success: true,
            message: '数据采集服务启动成功'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: '启动数据采集服务失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
router.post('/stop', (req, res) => {
    try {
        collectionService.stop();
        res.json({
            success: true,
            message: '数据采集服务停止成功'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: '停止数据采集服务失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
router.post('/manual/:lotteryCode', async (req, res) => {
    try {
        const { lotteryCode } = req.params;
        await collectionService.manualCollect(lotteryCode);
        res.json({
            success: true,
            message: `${lotteryCode} 数据采集完成`
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: '手动采集失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
router.put('/config/:lotteryCode', async (req, res) => {
    try {
        const { lotteryCode } = req.params;
        const { collectInterval, dataSourceUrl, collectWindowMinutes } = req.body;
        const config = {};
        if (collectInterval !== undefined) {
            if (typeof collectInterval !== 'number' || collectInterval < 1) {
                return res.status(400).json({
                    success: false,
                    message: '采集间隔必须是大于0的数字（秒）'
                });
            }
            config.collectInterval = collectInterval;
        }
        if (dataSourceUrl !== undefined) {
            if (typeof dataSourceUrl !== 'string' || !dataSourceUrl.trim()) {
                return res.status(400).json({
                    success: false,
                    message: '数据源URL不能为空'
                });
            }
            config.dataSourceUrl = dataSourceUrl.trim();
        }
        if (collectWindowMinutes !== undefined) {
            if (typeof collectWindowMinutes !== 'number' || collectWindowMinutes < 1) {
                return res.status(400).json({
                    success: false,
                    message: '采集窗口时间必须是大于0的数字（分钟）'
                });
            }
            config.collectWindowMinutes = collectWindowMinutes;
        }
        await collectionService.updateLotteryConfig(lotteryCode, config);
        return res.json({
            success: true,
            message: `${lotteryCode} 采集配置更新成功`
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: '更新采集配置失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
router.get('/config/:lotteryCode', async (req, res) => {
    try {
        const { lotteryCode } = req.params;
        const result = await require('../config/database').AppDataSource.query(`
      SELECT code, name, type, draw_time, draw_interval, collect_interval,
             data_source_type, data_source_url, status
      FROM lottery_types
      WHERE code = ?
    `, [lotteryCode]);
        if (result.length === 0) {
            return res.status(404).json({
                success: false,
                message: '彩种不存在'
            });
        }
        const lotteryType = result[0];
        return res.json({
            success: true,
            data: {
                code: lotteryType.code,
                name: lotteryType.name,
                type: lotteryType.type,
                drawTime: lotteryType.draw_time,
                drawInterval: lotteryType.draw_interval,
                collectInterval: lotteryType.collect_interval,
                dataSourceType: lotteryType.data_source_type,
                dataSourceUrl: lotteryType.data_source_url,
                status: lotteryType.status,
                isHighFrequency: lotteryType.draw_interval && lotteryType.draw_interval < 86400
            }
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: '获取采集配置失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
router.get('/config', async (req, res) => {
    try {
        const result = await require('../config/database').AppDataSource.query(`
      SELECT code, name, type, draw_time, draw_interval, collect_interval,
             data_source_type, data_source_url, status
      FROM lottery_types
      ORDER BY id
    `);
        const lotteryTypes = result.map((lt) => ({
            code: lt.code,
            name: lt.name,
            type: lt.type,
            drawTime: lt.draw_time,
            drawInterval: lt.draw_interval,
            collectInterval: lt.collect_interval,
            dataSourceType: lt.data_source_type,
            dataSourceUrl: lt.data_source_url,
            status: lt.status,
            isHighFrequency: lt.draw_interval && lt.draw_interval < 86400
        }));
        res.json({
            success: true,
            data: lotteryTypes
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: '获取采集配置失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
router.delete('/data/:lotteryCode', async (req, res) => {
    try {
        const { lotteryCode } = req.params;
        const lotteryResult = await require('../config/database').AppDataSource.query(`
      SELECT id, name FROM lottery_types WHERE code = ?
    `, [lotteryCode]);
        if (lotteryResult.length === 0) {
            return res.status(404).json({
                success: false,
                message: '彩种不存在'
            });
        }
        const lotteryTypeId = lotteryResult[0].id;
        const lotteryName = lotteryResult[0].name;
        const deleteResult = await require('../config/database').AppDataSource.query(`
      DELETE FROM lottery_results WHERE lottery_type_id = ?
    `, [lotteryTypeId]);
        await require('../config/database').AppDataSource.query(`
      DELETE FROM predictions WHERE lottery_type_id = ?
    `, [lotteryTypeId]);
        return res.json({
            success: true,
            message: `${lotteryName} 数据重置成功`,
            deletedCount: deleteResult.affectedRows
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: '数据重置失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
router.post('/init/:lotteryCode', async (req, res) => {
    try {
        const { lotteryCode } = req.params;
        await collectionService.initializeLotteryData(lotteryCode);
        return res.json({
            success: true,
            message: `${lotteryCode} 历史数据初始化成功`
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: '历史数据初始化失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
exports.default = router;
//# sourceMappingURL=collection.js.map