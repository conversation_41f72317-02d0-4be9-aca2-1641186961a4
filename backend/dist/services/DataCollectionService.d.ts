export declare class DataCollectionService {
    private static instance;
    private isRunning;
    private intervals;
    private constructor();
    static getInstance(): DataCollectionService;
    start(): Promise<void>;
    stop(): void;
    private getActiveLotteryTypes;
    private startCollectionForLotteryType;
    private collectDataForLotteryType;
    private collectFromTxtFile;
    private collectFromApi;
    private parseTxtContent;
    private parseTxtLine;
    private parseApiResponse;
    private saveCollectedData;
    manualCollect(lotteryCode: string): Promise<void>;
    private shouldCollectNow;
    updateLotteryConfig(lotteryCode: string, config: {
        collectInterval?: number;
        dataSourceUrl?: string;
        collectWindowMinutes?: number;
    }): Promise<void>;
    private restartCollectionForLottery;
    getStatus(): any;
}
//# sourceMappingURL=DataCollectionService.d.ts.map