"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataCollectionService = void 0;
const database_1 = require("../config/database");
const axios_1 = __importDefault(require("axios"));
class DataCollectionService {
    constructor() {
        this.isRunning = false;
        this.intervals = new Map();
    }
    static getInstance() {
        if (!DataCollectionService.instance) {
            DataCollectionService.instance = new DataCollectionService();
        }
        return DataCollectionService.instance;
    }
    async start() {
        if (this.isRunning) {
            console.log('⚠️  数据采集服务已在运行中');
            return;
        }
        console.log('🔄 启动数据采集服务...');
        this.isRunning = true;
        try {
            const lotteryTypes = await this.getActiveLotteryTypes();
            for (const lotteryType of lotteryTypes) {
                await this.startCollectionForLotteryType(lotteryType);
            }
            console.log('✅ 数据采集服务启动成功');
        }
        catch (error) {
            console.error('❌ 数据采集服务启动失败:', error);
            this.isRunning = false;
            throw error;
        }
    }
    stop() {
        console.log('🔄 停止数据采集服务...');
        this.intervals.forEach((interval, lotteryCode) => {
            clearInterval(interval);
            console.log(`⏹️  停止 ${lotteryCode} 数据采集`);
        });
        this.intervals.clear();
        this.isRunning = false;
        console.log('✅ 数据采集服务已停止');
    }
    async getActiveLotteryTypes() {
        const result = await database_1.AppDataSource.query(`
      SELECT id, code, name, type, draw_time, draw_interval, collect_interval,
             data_source_type, data_source_url, status
      FROM lottery_types 
      WHERE status = 'active'
    `);
        return result;
    }
    async startCollectionForLotteryType(lotteryType) {
        const { code, name, collect_interval, data_source_type, data_source_url } = lotteryType;
        if (!data_source_url) {
            console.log(`⚠️  ${name} 未配置数据源，跳过采集`);
            return;
        }
        console.log(`🚀 启动 ${name} 数据采集，间隔: ${collect_interval}秒`);
        if (await this.shouldCollectNow(lotteryType)) {
            await this.collectDataForLotteryType(lotteryType);
        }
        const interval = setInterval(async () => {
            try {
                if (await this.shouldCollectNow(lotteryType)) {
                    await this.collectDataForLotteryType(lotteryType);
                }
                else {
                    console.log(`⏰ ${name} 当前不在采集时间窗口内，跳过采集`);
                }
            }
            catch (error) {
                console.error(`❌ ${name} 数据采集失败:`, error);
            }
        }, collect_interval * 1000);
        this.intervals.set(code, interval);
    }
    async collectDataForLotteryType(lotteryType) {
        const { code, name, data_source_type, data_source_url } = lotteryType;
        console.log(`🔄 开始采集 ${name} 数据，数据源: ${data_source_url}`);
        try {
            let newData = [];
            if (data_source_type === 'txt') {
                console.log(`📄 从TXT文件采集 ${name} 数据...`);
                newData = await this.collectFromTxtFile(data_source_url, code);
            }
            else if (data_source_type === 'api') {
                console.log(`🔗 从API接口采集 ${name} 数据...`);
                newData = await this.collectFromApi(data_source_url, code);
            }
            else {
                console.log(`⚠️  ${name} 不支持的数据源类型: ${data_source_type}`);
                return;
            }
            console.log(`📊 ${name} 解析到 ${newData.length} 条数据`);
            if (newData.length > 0) {
                await this.saveCollectedData(lotteryType, newData);
                console.log(`✅ ${name} 采集到 ${newData.length} 条新数据`);
            }
            else {
                console.log(`ℹ️  ${name} 暂无新数据`);
            }
        }
        catch (error) {
            console.error(`❌ ${name} 数据采集失败:`, error);
            throw error;
        }
    }
    async collectFromTxtFile(url, lotteryCode) {
        try {
            console.log(`🌐 请求URL: ${url}`);
            const response = await axios_1.default.get(url, {
                timeout: 30000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });
            console.log(`📥 响应状态: ${response.status}, 数据长度: ${response.data.length}`);
            const content = response.data;
            const parsedData = this.parseTxtContent(content, lotteryCode);
            console.log(`🔍 解析结果: ${parsedData.length} 条记录`);
            return parsedData;
        }
        catch (error) {
            console.error('TXT文件采集失败:', error);
            return [];
        }
    }
    async collectFromApi(url, lotteryCode) {
        try {
            let requestUrl = url;
            if (lotteryCode === 'PC28') {
                const timestamp = Date.now();
                requestUrl = `${url}&V=${timestamp}`;
            }
            console.log(`🌐 请求API: ${requestUrl}`);
            const response = await axios_1.default.get(requestUrl, {
                timeout: 30000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });
            console.log(`📥 API响应状态: ${response.status}`);
            const parsedData = this.parseApiResponse(response.data, lotteryCode);
            console.log(`🔍 API解析结果: ${parsedData.length} 条记录`);
            return parsedData;
        }
        catch (error) {
            console.error('API接口采集失败:', error);
            return [];
        }
    }
    parseTxtContent(content, lotteryCode) {
        const lines = content.split('\n').filter(line => line.trim());
        const results = [];
        for (const line of lines) {
            const parsed = this.parseTxtLine(line.trim(), lotteryCode);
            if (parsed) {
                results.push(parsed);
            }
        }
        return results;
    }
    parseTxtLine(line, lotteryCode) {
        try {
            const parts = line.split(/\s+/);
            if (parts.length < 5) {
                return null;
            }
            const period = parts[0];
            const date = parts[1];
            const num1 = parts[2];
            const num2 = parts[3];
            const num3 = parts[4];
            if (lotteryCode === 'FC3D') {
                return {
                    period,
                    drawTime: `${date} 21:15:00`,
                    fc3dNumbers: `${num1}${num2}${num3}`
                };
            }
            else {
                if (parts.length < 7) {
                    return null;
                }
                const num4 = parts[5];
                const num5 = parts[6];
                return {
                    period,
                    drawTime: `${date} 21:30:00`,
                    pl3Numbers: `${num1}${num2}${num3}`,
                    pl4Numbers: `${num1}${num2}${num3}${num4}`,
                    pl5Numbers: `${num1}${num2}${num3}${num4}${num5}`
                };
            }
        }
        catch (error) {
            console.error('解析TXT行失败:', line, error);
            return null;
        }
    }
    parseApiResponse(data, lotteryCode) {
        try {
            if (lotteryCode === 'PC28') {
                return this.parsePC28ApiResponse(data);
            }
            console.log('未知彩种API响应数据:', data);
            return [];
        }
        catch (error) {
            console.error('解析API响应失败:', error);
            return [];
        }
    }
    parsePC28ApiResponse(data) {
        try {
            if (data.pre && data.pre.turnNum && data.pre.openNum && data.pre.openTime) {
                const { turnNum, openNum, openTime } = data.pre;
                const drawTime = new Date(openTime).toISOString().replace('T', ' ').substring(0, 19);
                return [{
                        period: turnNum,
                        drawTime: drawTime,
                        pc28Numbers: openNum
                    }];
            }
            return [];
        }
        catch (error) {
            console.error('解析PC28 API响应失败:', error);
            return [];
        }
    }
    async saveCollectedData(lotteryType, data) {
        const { id: lotteryTypeId, code } = lotteryType;
        for (const item of data) {
            try {
                let numbers = '';
                if (code === 'FC3D') {
                    numbers = item.fc3dNumbers;
                }
                else if (code === 'PL3') {
                    numbers = item.pl3Numbers;
                }
                else if (code === 'PL4') {
                    numbers = item.pl4Numbers;
                }
                else if (code === 'PL5') {
                    numbers = item.pl5Numbers;
                }
                else if (code === 'PC28') {
                    numbers = item.pc28Numbers;
                }
                else {
                    numbers = item.numbers || '';
                }
                if (!numbers) {
                    continue;
                }
                const existing = await database_1.AppDataSource.query('SELECT id FROM lottery_results WHERE lottery_type_id = ? AND period = ?', [lotteryTypeId, item.period]);
                if (existing.length === 0) {
                    await database_1.AppDataSource.query(`
            INSERT INTO lottery_results (lottery_type_id, period, draw_time, numbers)
            VALUES (?, ?, ?, ?)
          `, [lotteryTypeId, item.period, item.drawTime, numbers]);
                }
            }
            catch (error) {
                console.error('保存数据失败:', item, error);
            }
        }
    }
    async manualCollect(lotteryCode) {
        const lotteryTypes = await this.getActiveLotteryTypes();
        const lotteryType = lotteryTypes.find(lt => lt.code === lotteryCode);
        if (!lotteryType) {
            throw new Error(`彩种 ${lotteryCode} 不存在或未启用`);
        }
        if (lotteryCode === 'PC28') {
            await this.ensurePC28HistoricalData(lotteryType);
        }
        await this.collectDataForLotteryType(lotteryType);
    }
    async ensurePC28HistoricalData(lotteryType) {
        const { id: lotteryTypeId } = lotteryType;
        const countResult = await database_1.AppDataSource.query(`
      SELECT COUNT(*) as count FROM lottery_results WHERE lottery_type_id = ?
    `, [lotteryTypeId]);
        const currentCount = countResult[0].count;
        console.log(`📊 PC28当前数据量: ${currentCount} 条`);
        if (currentCount >= 2000) {
            console.log(`✅ PC28历史数据充足，无需补充`);
            return;
        }
        console.log(`🔄 PC28历史数据不足，开始补充历史数据...`);
        const today = new Date();
        const daysToFetch = Math.ceil((2000 - currentCount) / 400);
        for (let i = 0; i < daysToFetch && i < 10; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            console.log(`📅 获取 ${dateStr} 的PC28历史数据...`);
            await this.fetchPC28HistoricalDataForDate(lotteryType, dateStr);
            const newCountResult = await database_1.AppDataSource.query(`
        SELECT COUNT(*) as count FROM lottery_results WHERE lottery_type_id = ?
      `, [lotteryTypeId]);
            if (newCountResult[0].count >= 2000) {
                console.log(`✅ PC28历史数据补充完成，总计: ${newCountResult[0].count} 条`);
                break;
            }
        }
    }
    async fetchPC28HistoricalDataForDate(lotteryType, date) {
        try {
            const url = `https://23.248.233.80/anls-api/data/capc28/lotteryList/${date}.do`;
            console.log(`🌐 请求PC28历史数据: ${url}`);
            const response = await axios_1.default.get(url, {
                timeout: 30000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });
            const historicalData = this.parsePC28HistoricalData(response.data);
            if (historicalData.length > 0) {
                await this.saveCollectedData(lotteryType, historicalData);
                console.log(`✅ ${date} PC28历史数据保存成功: ${historicalData.length} 条`);
            }
            else {
                console.log(`ℹ️  ${date} 暂无PC28历史数据`);
            }
        }
        catch (error) {
            console.error(`❌ 获取 ${date} PC28历史数据失败:`, error);
        }
    }
    parsePC28HistoricalData(data) {
        try {
            if (!Array.isArray(data)) {
                return [];
            }
            return data.map(item => ({
                period: item.issue,
                drawTime: item.openDateTime,
                pc28Numbers: item.openNum.join(',')
            })).filter(item => item.period && item.drawTime && item.pc28Numbers);
        }
        catch (error) {
            console.error('解析PC28历史数据失败:', error);
            return [];
        }
    }
    async shouldCollectNow(lotteryType) {
        const { id, code, draw_time, draw_interval } = lotteryType;
        const now = new Date();
        if (draw_interval && draw_interval < 86400) {
            return true;
        }
        if (draw_time) {
            const today = new Date();
            const [hours, minutes, seconds] = draw_time.split(':').map(Number);
            const drawTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, seconds);
            const collectEndTime = new Date(drawTime.getTime() + 30 * 60 * 1000);
            const inTimeWindow = now >= drawTime && now <= collectEndTime;
            if (!inTimeWindow) {
                console.log(`⏰ ${code} 当前时间 ${now.toLocaleTimeString()} 不在采集窗口内 (${drawTime.toLocaleTimeString()} - ${collectEndTime.toLocaleTimeString()})`);
                return false;
            }
            const todayStr = today.toISOString().split('T')[0];
            const existingData = await database_1.AppDataSource.query(`
        SELECT COUNT(*) as count
        FROM lottery_results
        WHERE lottery_type_id = ? AND DATE(draw_time) = ?
      `, [id, todayStr]);
            const hasToday = existingData[0].count > 0;
            if (hasToday) {
                console.log(`✅ ${code} 今日数据已采集，跳过重复采集`);
                return false;
            }
            console.log(`🎯 ${code} 在采集窗口内且未采集今日数据，开始采集`);
            return true;
        }
        return true;
    }
    async updateLotteryConfig(lotteryCode, config) {
        const updateFields = [];
        const updateValues = [];
        if (config.collectInterval !== undefined) {
            updateFields.push('collect_interval = ?');
            updateValues.push(config.collectInterval);
        }
        if (config.dataSourceUrl !== undefined) {
            updateFields.push('data_source_url = ?');
            updateValues.push(config.dataSourceUrl);
        }
        if (updateFields.length > 0) {
            updateValues.push(lotteryCode);
            await database_1.AppDataSource.query(`
        UPDATE lottery_types
        SET ${updateFields.join(', ')}, updated_at = NOW()
        WHERE code = ?
      `, updateValues);
            console.log(`✅ 更新 ${lotteryCode} 采集配置成功`);
            if (this.isRunning && this.intervals.has(lotteryCode)) {
                await this.restartCollectionForLottery(lotteryCode);
            }
        }
    }
    async restartCollectionForLottery(lotteryCode) {
        const interval = this.intervals.get(lotteryCode);
        if (interval) {
            clearInterval(interval);
            this.intervals.delete(lotteryCode);
            console.log(`⏹️  停止 ${lotteryCode} 采集任务`);
        }
        const lotteryTypes = await this.getActiveLotteryTypes();
        const lotteryType = lotteryTypes.find(lt => lt.code === lotteryCode);
        if (lotteryType) {
            await this.startCollectionForLotteryType(lotteryType);
            console.log(`🔄 重启 ${lotteryCode} 采集任务`);
        }
    }
    getStatus() {
        return {
            isRunning: this.isRunning,
            activeCollectors: Array.from(this.intervals.keys()),
            totalCollectors: this.intervals.size
        };
    }
}
exports.DataCollectionService = DataCollectionService;
//# sourceMappingURL=DataCollectionService.js.map