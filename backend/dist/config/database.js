"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppDataSource = void 0;
exports.testDatabaseConnection = testDatabaseConnection;
exports.closeDatabaseConnection = closeDatabaseConnection;
const typeorm_1 = require("typeorm");
const path_1 = __importDefault(require("path"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
exports.AppDataSource = new typeorm_1.DataSource({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USERNAME || 'lottery_analysis',
    password: process.env.DB_PASSWORD || 'lottery_analysis',
    database: process.env.DB_DATABASE || 'lottery_analysis',
    synchronize: process.env.NODE_ENV === 'development',
    logging: process.env.NODE_ENV === 'development',
    entities: [
        path_1.default.join(__dirname, '../models/*.{ts,js}')
    ],
    migrations: [
        path_1.default.join(__dirname, '../migrations/*.{ts,js}')
    ],
    subscribers: [
        path_1.default.join(__dirname, '../subscribers/*.{ts,js}')
    ],
    extra: {
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000,
        reconnect: true
    },
    charset: 'utf8mb4'
});
async function testDatabaseConnection() {
    try {
        if (!exports.AppDataSource.isInitialized) {
            await exports.AppDataSource.initialize();
        }
        await exports.AppDataSource.query('SELECT 1');
        console.log('✅ 数据库连接测试成功');
        return true;
    }
    catch (error) {
        console.error('❌ 数据库连接测试失败:', error);
        return false;
    }
}
async function closeDatabaseConnection() {
    try {
        if (exports.AppDataSource.isInitialized) {
            await exports.AppDataSource.destroy();
            console.log('✅ 数据库连接已关闭');
        }
    }
    catch (error) {
        console.error('❌ 关闭数据库连接失败:', error);
    }
}
process.on('SIGINT', async () => {
    await closeDatabaseConnection();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    await closeDatabaseConnection();
    process.exit(0);
});
//# sourceMappingURL=database.js.map