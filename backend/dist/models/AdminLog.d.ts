import { User } from './User';
export declare class AdminLog {
    id: number;
    userId: number;
    action: string;
    description: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
    result: 'success' | 'failed' | 'warning';
    createdAt: Date;
    user: User;
    static createUserLog(userId: number, action: string, description: string, targetUserId?: number, ipAddress?: string, userAgent?: string): AdminLog;
    static createLotteryLog(userId: number, action: string, description: string, lotteryTypeId?: string, ipAddress?: string, userAgent?: string): AdminLog;
    static createSystemLog(userId: number, action: string, description: string, details?: any, ipAddress?: string, userAgent?: string): AdminLog;
    static createFailedLog(userId: number, action: string, description: string, error?: string, ipAddress?: string, userAgent?: string): AdminLog;
    getActionDescription(): string;
    getResultDescription(): string;
    getInfo(): {
        id: number;
        userId: number;
        action: string;
        actionDescription: string;
        description: string;
        details: any;
        ipAddress: string | undefined;
        userAgent: string | undefined;
        result: "warning" | "success" | "failed";
        resultDescription: string;
        createdAt: Date;
    };
    toApiResponse(): {
        id: number;
        userId: number;
        username: string;
        action: string;
        actionDescription: string;
        description: string;
        result: "warning" | "success" | "failed";
        resultDescription: string;
        ipAddress: string | undefined;
        createdAt: string;
    };
}
//# sourceMappingURL=AdminLog.d.ts.map