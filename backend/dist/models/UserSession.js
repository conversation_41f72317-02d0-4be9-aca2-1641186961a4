"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSession = void 0;
const typeorm_1 = require("typeorm");
const User_1 = require("./User");
let UserSession = class UserSession {
    isValid() {
        return this.status === 'active' && new Date() < this.expiresAt;
    }
    isExpired() {
        return new Date() >= this.expiresAt;
    }
    updateActivity() {
        this.lastActivityAt = new Date();
    }
    extend(hours = 24) {
        this.expiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);
        this.updateActivity();
    }
    revoke() {
        this.status = 'revoked';
    }
    markExpired() {
        this.status = 'expired';
    }
    getRemainingMinutes() {
        if (this.isExpired()) {
            return 0;
        }
        const now = new Date();
        const diffTime = this.expiresAt.getTime() - now.getTime();
        return Math.ceil(diffTime / (1000 * 60));
    }
    isSameDevice(ipAddress, userAgent) {
        return this.ipAddress === ipAddress && this.userAgent === userAgent;
    }
    static parseDeviceType(userAgent) {
        if (!userAgent) {
            return 'unknown';
        }
        const ua = userAgent.toLowerCase();
        if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
            return 'mobile';
        }
        else if (ua.includes('tablet') || ua.includes('ipad')) {
            return 'tablet';
        }
        else {
            return 'desktop';
        }
    }
    getInfo() {
        return {
            id: this.id,
            userId: this.userId,
            token: this.token,
            status: this.status,
            ipAddress: this.ipAddress,
            userAgent: this.userAgent,
            deviceType: this.deviceType,
            expiresAt: this.expiresAt,
            lastActivityAt: this.lastActivityAt,
            isValid: this.isValid(),
            isExpired: this.isExpired(),
            remainingMinutes: this.getRemainingMinutes(),
            createdAt: this.createdAt
        };
    }
    toApiResponse() {
        return {
            id: this.id,
            status: this.status,
            deviceType: this.deviceType,
            ipAddress: this.ipAddress,
            lastActivityAt: this.lastActivityAt?.toISOString(),
            expiresAt: this.expiresAt.toISOString(),
            createdAt: this.createdAt.toISOString()
        };
    }
};
exports.UserSession = UserSession;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '主键ID' }),
    __metadata("design:type", Number)
], UserSession.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', comment: '用户ID' }),
    __metadata("design:type", Number)
], UserSession.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, unique: true, comment: '会话令牌' }),
    __metadata("design:type", String)
], UserSession.prototype, "token", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'expired', 'revoked'],
        default: 'active',
        comment: '会话状态'
    }),
    __metadata("design:type", String)
], UserSession.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 45, nullable: true, comment: '客户端IP地址' }),
    __metadata("design:type", String)
], UserSession.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '用户代理字符串' }),
    __metadata("design:type", String)
], UserSession.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true, comment: '设备类型' }),
    __metadata("design:type", String)
], UserSession.prototype, "deviceType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', comment: '过期时间' }),
    __metadata("design:type", Date)
], UserSession.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true, comment: '最后活动时间' }),
    __metadata("design:type", Date)
], UserSession.prototype, "lastActivityAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    __metadata("design:type", Date)
], UserSession.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    __metadata("design:type", Date)
], UserSession.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User_1.User, user => user.sessions),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", User_1.User)
], UserSession.prototype, "user", void 0);
exports.UserSession = UserSession = __decorate([
    (0, typeorm_1.Entity)('user_sessions'),
    (0, typeorm_1.Index)(['userId', 'status']),
    (0, typeorm_1.Index)(['token'], { unique: true }),
    (0, typeorm_1.Index)(['expiresAt'])
], UserSession);
//# sourceMappingURL=UserSession.js.map