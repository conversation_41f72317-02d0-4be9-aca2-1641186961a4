"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserMembership = void 0;
const typeorm_1 = require("typeorm");
const User_1 = require("./User");
const LotteryType_1 = require("./LotteryType");
let UserMembership = class UserMembership {
    isValid() {
        const now = new Date();
        return this.status === 'active' &&
            this.startDate <= now &&
            this.endDate > now;
    }
    isExpired() {
        return new Date() >= this.endDate;
    }
    getRemainingDays() {
        if (this.isExpired()) {
            return 0;
        }
        const now = new Date();
        const diffTime = this.endDate.getTime() - now.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    extend(days) {
        const currentExpiry = this.isExpired() ? new Date() : this.endDate;
        this.endDate = new Date(currentExpiry.getTime() + days * 24 * 60 * 60 * 1000);
        if (this.status === 'expired') {
            this.status = 'active';
        }
    }
    suspend() {
        this.status = 'suspended';
    }
    activate() {
        if (!this.isExpired()) {
            this.status = 'active';
        }
    }
    updateExpiredStatus() {
        if (this.isExpired() && this.status === 'active') {
            this.status = 'expired';
        }
    }
    getInfo() {
        return {
            id: this.id,
            userId: this.userId,
            lotteryTypeId: this.lotteryTypeId,
            status: this.status,
            startDate: this.startDate,
            endDate: this.endDate,
            isValid: this.isValid(),
            isExpired: this.isExpired(),
            remainingDays: this.getRemainingDays(),
            createdAt: this.createdAt
        };
    }
    toApiResponse() {
        return {
            id: this.id,
            lotteryTypeId: this.lotteryTypeId,
            lotteryTypeName: this.lotteryType?.name,
            status: this.status,
            startDate: this.startDate.toISOString(),
            endDate: this.endDate.toISOString(),
            isValid: this.isValid(),
            remainingDays: this.getRemainingDays()
        };
    }
};
exports.UserMembership = UserMembership;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '主键ID' }),
    __metadata("design:type", Number)
], UserMembership.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', type: 'int', comment: '用户ID' }),
    __metadata("design:type", Number)
], UserMembership.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lottery_type_id', type: 'int', comment: '彩种ID' }),
    __metadata("design:type", Number)
], UserMembership.prototype, "lotteryTypeId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'expired', 'suspended'],
        default: 'active',
        comment: '会员状态'
    }),
    __metadata("design:type", String)
], UserMembership.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'start_date', type: 'date', comment: '开始日期' }),
    __metadata("design:type", Date)
], UserMembership.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'end_date', type: 'date', comment: '结束日期' }),
    __metadata("design:type", Date)
], UserMembership.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '创建时间' }),
    __metadata("design:type", Date)
], UserMembership.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at', comment: '更新时间' }),
    __metadata("design:type", Date)
], UserMembership.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User_1.User, user => user.memberships),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", User_1.User)
], UserMembership.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => LotteryType_1.LotteryType),
    (0, typeorm_1.JoinColumn)({ name: 'lottery_type_id' }),
    __metadata("design:type", LotteryType_1.LotteryType)
], UserMembership.prototype, "lotteryType", void 0);
exports.UserMembership = UserMembership = __decorate([
    (0, typeorm_1.Entity)('user_memberships')
], UserMembership);
//# sourceMappingURL=UserMembership.js.map