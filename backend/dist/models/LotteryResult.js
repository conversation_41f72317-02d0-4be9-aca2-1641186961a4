"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LotteryResult = void 0;
const typeorm_1 = require("typeorm");
const LotteryType_1 = require("./LotteryType");
let LotteryResult = class LotteryResult {
    getNumbersArray() {
        return this.numbers.split('');
    }
    getNumbersString() {
        return this.numbers;
    }
    getFormattedNumbers() {
        return this.numbers.split('').join(' ');
    }
    containsNumber(number) {
        return this.numbers.includes(number);
    }
    containsNumbers(numbers) {
        const numbersArray = this.getNumbersArray();
        return numbers.every(num => numbersArray.includes(num));
    }
    getNumberAtPosition(position) {
        return this.numbers[position];
    }
    getSameNumberCount(other) {
        const thisNumbers = this.getNumbersArray();
        const otherNumbers = other.getNumbersArray();
        return thisNumbers.filter(num => otherNumbers.includes(num)).length;
    }
    getInfo() {
        return {
            id: this.id,
            lotteryTypeId: this.lotteryTypeId,
            period: this.period,
            drawTime: this.drawTime,
            numbers: this.numbers,
            numbersArray: this.getNumbersArray(),
            numbersString: this.getNumbersString(),
            formattedNumbers: this.getFormattedNumbers()
        };
    }
    toApiResponse() {
        return {
            period: this.period,
            drawTime: this.drawTime.toISOString(),
            numbers: this.getNumbersArray(),
            numbersDisplay: this.getFormattedNumbers()
        };
    }
};
exports.LotteryResult = LotteryResult;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'bigint', comment: '主键ID' }),
    __metadata("design:type", Number)
], LotteryResult.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lottery_type_id', type: 'int', comment: '彩种ID' }),
    __metadata("design:type", Number)
], LotteryResult.prototype, "lotteryTypeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, comment: '期号' }),
    __metadata("design:type", String)
], LotteryResult.prototype, "period", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'draw_time', type: 'datetime', comment: '开奖时间' }),
    __metadata("design:type", Date)
], LotteryResult.prototype, "drawTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, comment: '开奖号码字符串' }),
    __metadata("design:type", String)
], LotteryResult.prototype, "numbers", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => LotteryType_1.LotteryType, lotteryType => lotteryType.results),
    (0, typeorm_1.JoinColumn)({ name: 'lottery_type_id' }),
    __metadata("design:type", LotteryType_1.LotteryType)
], LotteryResult.prototype, "lotteryType", void 0);
exports.LotteryResult = LotteryResult = __decorate([
    (0, typeorm_1.Entity)('lottery_results'),
    (0, typeorm_1.Index)(['lotteryTypeId', 'period'], { unique: true }),
    (0, typeorm_1.Index)(['lotteryTypeId', 'drawTime'])
], LotteryResult);
//# sourceMappingURL=LotteryResult.js.map