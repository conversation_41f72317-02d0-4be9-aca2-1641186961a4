import { UserMembership } from './UserMembership';
export declare class User {
    id: number;
    username: string;
    nickname: string;
    password: string;
    status: 'active' | 'inactive';
    createdAt: Date;
    updatedAt: Date;
    memberships: UserMembership[];
    isActive(): boolean;
    getActiveMemberships(): UserMembership[];
    hasMembershipForLottery(lotteryTypeId: number): boolean;
    getInfo(): {
        id: number;
        username: string;
        nickname: string;
        status: "active" | "inactive";
        createdAt: Date;
    };
    toApiResponse(): {
        id: number;
        username: string;
        nickname: string;
        status: "active" | "inactive";
        createdAt: string;
    };
}
//# sourceMappingURL=User.d.ts.map