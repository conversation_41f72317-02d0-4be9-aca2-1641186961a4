import { User } from './User';
export declare class UserSession {
    id: number;
    userId: number;
    token: string;
    status: 'active' | 'expired' | 'revoked';
    ipAddress?: string;
    userAgent?: string;
    deviceType?: string;
    expiresAt: Date;
    lastActivityAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    user: User;
    isValid(): boolean;
    isExpired(): boolean;
    updateActivity(): void;
    extend(hours?: number): void;
    revoke(): void;
    markExpired(): void;
    getRemainingMinutes(): number;
    isSameDevice(ipAddress: string, userAgent: string): boolean;
    static parseDeviceType(userAgent: string): string;
    getInfo(): {
        id: number;
        userId: number;
        token: string;
        status: "active" | "expired" | "revoked";
        ipAddress: string | undefined;
        userAgent: string | undefined;
        deviceType: string | undefined;
        expiresAt: Date;
        lastActivityAt: Date | undefined;
        isValid: boolean;
        isExpired: boolean;
        remainingMinutes: number;
        createdAt: Date;
    };
    toApiResponse(): {
        id: number;
        status: "active" | "expired" | "revoked";
        deviceType: string | undefined;
        ipAddress: string | undefined;
        lastActivityAt: string | undefined;
        expiresAt: string;
        createdAt: string;
    };
}
//# sourceMappingURL=UserSession.d.ts.map