"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.entities = exports.UserMembership = exports.User = exports.Prediction = exports.LotteryResult = exports.LotteryType = void 0;
var LotteryType_1 = require("./LotteryType");
Object.defineProperty(exports, "LotteryType", { enumerable: true, get: function () { return LotteryType_1.LotteryType; } });
var LotteryResult_1 = require("./LotteryResult");
Object.defineProperty(exports, "LotteryResult", { enumerable: true, get: function () { return LotteryResult_1.LotteryResult; } });
var Prediction_1 = require("./Prediction");
Object.defineProperty(exports, "Prediction", { enumerable: true, get: function () { return Prediction_1.Prediction; } });
var User_1 = require("./User");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return User_1.User; } });
var UserMembership_1 = require("./UserMembership");
Object.defineProperty(exports, "UserMembership", { enumerable: true, get: function () { return UserMembership_1.UserMembership; } });
exports.entities = [
    require('./LotteryType').LotteryType,
    require('./LotteryResult').LotteryResult,
    require('./Prediction').Prediction,
    require('./User').User,
    require('./UserMembership').UserMembership
];
//# sourceMappingURL=index.js.map