import { LotteryType } from './LotteryType';
export declare class Prediction {
    id: number;
    lotteryTypeId: number;
    targetPeriod: string;
    prediction: string;
    createdAt: Date;
    lotteryType: LotteryType;
    getPredictionsArray(): string[];
    getPredictionsString(): string;
    getFormattedPredictions(): string;
    containsPrediction(number: string): boolean;
    getPredictionCount(): number;
    checkHits(actualNumbers: string[]): {
        hits: string[];
        hitCount: number;
        hitRate: number;
    };
    getInfo(): {
        id: number;
        lotteryTypeId: number;
        targetPeriod: string;
        prediction: string;
        predictionsArray: string[];
        predictionsString: string;
        formattedPredictions: string;
        predictionCount: number;
        createdAt: Date;
    };
    toApiResponse(): {
        targetPeriod: string;
        predictions: string[];
        predictionsDisplay: string;
        createdAt: string;
    };
    toApiResponseWithHits(actualNumbers?: string[]): {
        targetPeriod: string;
        predictions: string[];
        predictionsDisplay: string;
        createdAt: string;
    } | {
        hits: string[];
        hitCount: number;
        hitRate: number;
        targetPeriod: string;
        predictions: string[];
        predictionsDisplay: string;
        createdAt: string;
    };
}
//# sourceMappingURL=Prediction.d.ts.map