{"version": 3, "file": "UserMembership.js", "sourceRoot": "", "sources": ["../../src/models/UserMembership.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAA4H;AAC5H,iCAA8B;AAC9B,+CAA4C;AAMrC,IAAM,cAAc,GAApB,MAAM,cAAc;IA0CzB,OAAO;QACL,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ;YACxB,IAAI,CAAC,SAAS,IAAI,GAAG;YACrB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;IAC5B,CAAC;IAKD,SAAS;QACP,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC;IACpC,CAAC;IAKD,gBAAgB;QACd,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACrB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;QACxD,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAKD,MAAM,CAAC,IAAY;QACjB,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;QACnE,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAE9E,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACzB,CAAC;IACH,CAAC;IAKD,OAAO;QACL,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;IAC5B,CAAC;IAKD,QAAQ;QACN,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACzB,CAAC;IACH,CAAC;IAKD,mBAAmB;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QAC1B,CAAC;IACH,CAAC;IAKD,OAAO;QACL,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI;YACvC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;YACvB,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;SACvC,CAAC;IACJ,CAAC;CACF,CAAA;AA3IY,wCAAc;AAEzB;IADC,IAAA,gCAAsB,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;0CAChC;AAGZ;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;8CAC1C;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;qDAC3C;AAQvB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;QACxC,OAAO,EAAE,QAAQ;QACjB,OAAO,EAAE,MAAM;KAChB,CAAC;;8CAC0C;AAG5C;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;8BAClD,IAAI;iDAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;8BAClD,IAAI;+CAAC;AAGf;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;8BAC9C,IAAI;iDAAC;AAGjB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;8BAC9C,IAAI;iDAAC;AAKjB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,WAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC;IAC/C,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;8BACzB,WAAI;4CAAC;AAIZ;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,yBAAW,CAAC;IAC5B,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;8BAC1B,yBAAW;mDAAC;yBArCf,cAAc;IAD1B,IAAA,gBAAM,EAAC,kBAAkB,CAAC;GACd,cAAc,CA2I1B"}