"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AdminLog_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminLog = void 0;
const typeorm_1 = require("typeorm");
const User_1 = require("./User");
let AdminLog = AdminLog_1 = class AdminLog {
    static createUserLog(userId, action, description, targetUserId, ipAddress, userAgent) {
        const log = new AdminLog_1();
        log.userId = userId;
        log.action = action;
        log.description = description;
        log.ipAddress = ipAddress;
        log.userAgent = userAgent;
        log.result = 'success';
        if (targetUserId) {
            log.details = { targetUserId };
        }
        return log;
    }
    static createLotteryLog(userId, action, description, lotteryTypeId, ipAddress, userAgent) {
        const log = new AdminLog_1();
        log.userId = userId;
        log.action = action;
        log.description = description;
        log.ipAddress = ipAddress;
        log.userAgent = userAgent;
        log.result = 'success';
        if (lotteryTypeId) {
            log.details = { lotteryTypeId };
        }
        return log;
    }
    static createSystemLog(userId, action, description, details, ipAddress, userAgent) {
        const log = new AdminLog_1();
        log.userId = userId;
        log.action = action;
        log.description = description;
        log.details = details;
        log.ipAddress = ipAddress;
        log.userAgent = userAgent;
        log.result = 'success';
        return log;
    }
    static createFailedLog(userId, action, description, error, ipAddress, userAgent) {
        const log = new AdminLog_1();
        log.userId = userId;
        log.action = action;
        log.description = description;
        log.result = 'failed';
        log.ipAddress = ipAddress;
        log.userAgent = userAgent;
        if (error) {
            log.details = { error };
        }
        return log;
    }
    getActionDescription() {
        const actionMap = {
            'user.create': '创建用户',
            'user.update': '更新用户',
            'user.delete': '删除用户',
            'user.ban': '封禁用户',
            'user.unban': '解封用户',
            'membership.create': '创建会员权限',
            'membership.update': '更新会员权限',
            'membership.delete': '删除会员权限',
            'lottery.create': '创建彩种',
            'lottery.update': '更新彩种',
            'lottery.delete': '删除彩种',
            'system.backup': '系统备份',
            'system.restore': '系统恢复',
            'system.config': '系统配置',
            'login': '管理员登录',
            'logout': '管理员登出'
        };
        return actionMap[this.action] || this.action;
    }
    getResultDescription() {
        const resultMap = {
            'success': '成功',
            'failed': '失败',
            'warning': '警告'
        };
        return resultMap[this.result] || this.result;
    }
    getInfo() {
        return {
            id: this.id,
            userId: this.userId,
            action: this.action,
            actionDescription: this.getActionDescription(),
            description: this.description,
            details: this.details,
            ipAddress: this.ipAddress,
            userAgent: this.userAgent,
            result: this.result,
            resultDescription: this.getResultDescription(),
            createdAt: this.createdAt
        };
    }
    toApiResponse() {
        return {
            id: this.id,
            userId: this.userId,
            username: this.user?.username,
            action: this.action,
            actionDescription: this.getActionDescription(),
            description: this.description,
            result: this.result,
            resultDescription: this.getResultDescription(),
            ipAddress: this.ipAddress,
            createdAt: this.createdAt.toISOString()
        };
    }
};
exports.AdminLog = AdminLog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '主键ID' }),
    __metadata("design:type", Number)
], AdminLog.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', comment: '操作用户ID' }),
    __metadata("design:type", Number)
], AdminLog.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, comment: '操作类型' }),
    __metadata("design:type", String)
], AdminLog.prototype, "action", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, comment: '操作描述' }),
    __metadata("design:type", String)
], AdminLog.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true, comment: '操作详细数据' }),
    __metadata("design:type", Object)
], AdminLog.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 45, nullable: true, comment: '操作IP地址' }),
    __metadata("design:type", String)
], AdminLog.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '用户代理字符串' }),
    __metadata("design:type", String)
], AdminLog.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['success', 'failed', 'warning'],
        default: 'success',
        comment: '操作结果'
    }),
    __metadata("design:type", String)
], AdminLog.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '操作时间' }),
    __metadata("design:type", Date)
], AdminLog.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User_1.User, user => user.adminLogs),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", User_1.User)
], AdminLog.prototype, "user", void 0);
exports.AdminLog = AdminLog = AdminLog_1 = __decorate([
    (0, typeorm_1.Entity)('admin_logs'),
    (0, typeorm_1.Index)(['userId', 'createdAt']),
    (0, typeorm_1.Index)(['action']),
    (0, typeorm_1.Index)(['createdAt'])
], AdminLog);
//# sourceMappingURL=AdminLog.js.map