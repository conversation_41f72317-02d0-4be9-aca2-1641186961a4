import { LotteryType } from './LotteryType';
export declare class LotteryResult {
    id: number;
    lotteryTypeId: number;
    period: string;
    drawTime: Date;
    numbers: string;
    lotteryType: LotteryType;
    getNumbersArray(): string[];
    getNumbersString(): string;
    getFormattedNumbers(): string;
    containsNumber(number: string): boolean;
    containsNumbers(numbers: string[]): boolean;
    getNumberAtPosition(position: number): string | undefined;
    getSameNumberCount(other: LotteryResult): number;
    getInfo(): {
        id: number;
        lotteryTypeId: number;
        period: string;
        drawTime: Date;
        numbers: string;
        numbersArray: string[];
        numbersString: string;
        formattedNumbers: string;
    };
    toApiResponse(): {
        period: string;
        drawTime: string;
        numbers: string[];
        numbersDisplay: string;
    };
}
//# sourceMappingURL=LotteryResult.d.ts.map