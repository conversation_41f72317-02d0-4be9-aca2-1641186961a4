import { LotteryResult } from './LotteryResult';
import { Prediction } from './Prediction';
export declare class LotteryType {
    id: number;
    code: string;
    name: string;
    type: '3star' | '4star' | '5star';
    drawTime?: string;
    drawInterval?: number;
    collectInterval: number;
    dataSourceType?: 'txt' | 'api';
    dataSourceUrl?: string;
    autoClean: boolean;
    cleanThreshold: number;
    cleanKeep: number;
    status: 'active' | 'inactive';
    createdAt: Date;
    updatedAt: Date;
    results: LotteryResult[];
    predictions: Prediction[];
    getLatestResult(): LotteryResult | undefined;
    getNextDrawTime(): Date;
    isDrawTime(): boolean;
    isHighFrequency(): boolean;
    isEnabled(): boolean;
    getConfig(): {
        id: number;
        code: string;
        name: string;
        type: "3star" | "4star" | "5star";
        drawTime: string | undefined;
        drawInterval: number | undefined;
        collectInterval: number;
        dataSourceType: "txt" | "api" | undefined;
        dataSourceUrl: string | undefined;
        status: "active" | "inactive";
        isEnabled: boolean;
        isHighFrequency: boolean;
    };
}
//# sourceMappingURL=LotteryType.d.ts.map