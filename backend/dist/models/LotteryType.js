"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LotteryType = void 0;
const typeorm_1 = require("typeorm");
const LotteryResult_1 = require("./LotteryResult");
const Prediction_1 = require("./Prediction");
let LotteryType = class LotteryType {
    getLatestResult() {
        if (!this.results || this.results.length === 0) {
            return undefined;
        }
        return this.results.sort((a, b) => new Date(b.drawTime).getTime() - new Date(a.drawTime).getTime())[0];
    }
    getNextDrawTime() {
        const latestResult = this.getLatestResult();
        if (!latestResult) {
            return new Date();
        }
        const lastDrawTime = new Date(latestResult.drawTime);
        if (this.drawInterval && this.drawInterval < 86400) {
            return new Date(lastDrawTime.getTime() + this.drawInterval * 1000);
        }
        else {
            const nextDay = new Date(lastDrawTime);
            nextDay.setDate(nextDay.getDate() + 1);
            return nextDay;
        }
    }
    isDrawTime() {
        const now = new Date();
        const nextDrawTime = this.getNextDrawTime();
        return now >= nextDrawTime;
    }
    isHighFrequency() {
        return this.drawInterval !== undefined && this.drawInterval < 86400;
    }
    isEnabled() {
        return this.status === 'active';
    }
    getConfig() {
        return {
            id: this.id,
            code: this.code,
            name: this.name,
            type: this.type,
            drawTime: this.drawTime,
            drawInterval: this.drawInterval,
            collectInterval: this.collectInterval,
            dataSourceType: this.dataSourceType,
            dataSourceUrl: this.dataSourceUrl,
            status: this.status,
            isEnabled: this.isEnabled(),
            isHighFrequency: this.isHighFrequency()
        };
    }
};
exports.LotteryType = LotteryType;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '主键ID' }),
    __metadata("design:type", Number)
], LotteryType.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, unique: true, comment: '彩种代码' }),
    __metadata("design:type", String)
], LotteryType.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, comment: '彩种名称' }),
    __metadata("design:type", String)
], LotteryType.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['3star', '4star', '5star'],
        comment: '彩种类型'
    }),
    __metadata("design:type", String)
], LotteryType.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'time', nullable: true, comment: '开奖时间' }),
    __metadata("design:type", String)
], LotteryType.prototype, "drawTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true, comment: '开奖间隔(秒)' }),
    __metadata("design:type", Number)
], LotteryType.prototype, "drawInterval", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 5, comment: '采集间隔(秒)' }),
    __metadata("design:type", Number)
], LotteryType.prototype, "collectInterval", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['txt', 'api'],
        nullable: true,
        comment: '数据源类型'
    }),
    __metadata("design:type", String)
], LotteryType.prototype, "dataSourceType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true, comment: '数据源URL' }),
    __metadata("design:type", String)
], LotteryType.prototype, "dataSourceUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false, comment: '自动清理' }),
    __metadata("design:type", Boolean)
], LotteryType.prototype, "autoClean", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 5000, comment: '清理阈值' }),
    __metadata("design:type", Number)
], LotteryType.prototype, "cleanThreshold", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 2000, comment: '保留数量' }),
    __metadata("design:type", Number)
], LotteryType.prototype, "cleanKeep", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'inactive'],
        default: 'active',
        comment: '状态'
    }),
    __metadata("design:type", String)
], LotteryType.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    __metadata("design:type", Date)
], LotteryType.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    __metadata("design:type", Date)
], LotteryType.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => LotteryResult_1.LotteryResult, result => result.lotteryType),
    __metadata("design:type", Array)
], LotteryType.prototype, "results", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Prediction_1.Prediction, prediction => prediction.lotteryType),
    __metadata("design:type", Array)
], LotteryType.prototype, "predictions", void 0);
exports.LotteryType = LotteryType = __decorate([
    (0, typeorm_1.Entity)('lottery_types')
], LotteryType);
//# sourceMappingURL=LotteryType.js.map