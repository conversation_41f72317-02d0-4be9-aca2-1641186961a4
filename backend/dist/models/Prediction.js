"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Prediction = void 0;
const typeorm_1 = require("typeorm");
const LotteryType_1 = require("./LotteryType");
let Prediction = class Prediction {
    getPredictionsArray() {
        return this.prediction.split('');
    }
    getPredictionsString() {
        return this.prediction;
    }
    getFormattedPredictions() {
        return this.prediction.split('').join(' ');
    }
    containsPrediction(number) {
        return this.prediction.includes(number);
    }
    getPredictionCount() {
        return this.prediction.length;
    }
    checkHits(actualNumbers) {
        const predictions = this.getPredictionsArray();
        const hits = predictions.filter(pred => actualNumbers.includes(pred));
        const hitCount = hits.length;
        const hitRate = predictions.length > 0 ? hitCount / predictions.length : 0;
        return {
            hits,
            hitCount,
            hitRate
        };
    }
    getInfo() {
        return {
            id: this.id,
            lotteryTypeId: this.lotteryTypeId,
            targetPeriod: this.targetPeriod,
            prediction: this.prediction,
            predictionsArray: this.getPredictionsArray(),
            predictionsString: this.getPredictionsString(),
            formattedPredictions: this.getFormattedPredictions(),
            predictionCount: this.getPredictionCount(),
            createdAt: this.createdAt
        };
    }
    toApiResponse() {
        return {
            targetPeriod: this.targetPeriod,
            predictions: this.getPredictionsArray(),
            predictionsDisplay: this.getFormattedPredictions(),
            createdAt: this.createdAt.toISOString()
        };
    }
    toApiResponseWithHits(actualNumbers) {
        const baseResponse = this.toApiResponse();
        if (actualNumbers) {
            const hitInfo = this.checkHits(actualNumbers);
            return {
                ...baseResponse,
                hits: hitInfo.hits,
                hitCount: hitInfo.hitCount,
                hitRate: hitInfo.hitRate
            };
        }
        return baseResponse;
    }
};
exports.Prediction = Prediction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'bigint', comment: '主键ID' }),
    __metadata("design:type", Number)
], Prediction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lottery_type_id', type: 'int', comment: '彩种ID' }),
    __metadata("design:type", Number)
], Prediction.prototype, "lotteryTypeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'target_period', type: 'varchar', length: 20, comment: '预测期号' }),
    __metadata("design:type", String)
], Prediction.prototype, "targetPeriod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, comment: '预测胆码字符串' }),
    __metadata("design:type", String)
], Prediction.prototype, "prediction", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '预测生成时间' }),
    __metadata("design:type", Date)
], Prediction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => LotteryType_1.LotteryType, lotteryType => lotteryType.predictions),
    (0, typeorm_1.JoinColumn)({ name: 'lottery_type_id' }),
    __metadata("design:type", LotteryType_1.LotteryType)
], Prediction.prototype, "lotteryType", void 0);
exports.Prediction = Prediction = __decorate([
    (0, typeorm_1.Entity)('predictions'),
    (0, typeorm_1.Index)(['lotteryTypeId', 'targetPeriod'], { unique: true }),
    (0, typeorm_1.Index)(['lotteryTypeId', 'createdAt'])
], Prediction);
//# sourceMappingURL=Prediction.js.map