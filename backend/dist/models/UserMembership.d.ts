import { User } from './User';
import { LotteryType } from './LotteryType';
export declare class UserMembership {
    id: number;
    userId: number;
    lotteryTypeId: number;
    status: 'active' | 'expired' | 'suspended';
    startDate: Date;
    endDate: Date;
    createdAt: Date;
    updatedAt: Date;
    user: User;
    lotteryType: LotteryType;
    isValid(): boolean;
    isExpired(): boolean;
    getRemainingDays(): number;
    extend(days: number): void;
    suspend(): void;
    activate(): void;
    updateExpiredStatus(): void;
    getInfo(): {
        id: number;
        userId: number;
        lotteryTypeId: number;
        status: "active" | "expired" | "suspended";
        startDate: Date;
        endDate: Date;
        isValid: boolean;
        isExpired: boolean;
        remainingDays: number;
        createdAt: Date;
    };
    toApiResponse(): {
        id: number;
        lotteryTypeId: number;
        lotteryTypeName: string;
        status: "active" | "expired" | "suspended";
        startDate: string;
        endDate: string;
        isValid: boolean;
        remainingDays: number;
    };
}
//# sourceMappingURL=UserMembership.d.ts.map