{"version": 3, "file": "UserSession.js", "sourceRoot": "", "sources": ["../../src/models/UserSession.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAAmI;AACnI,iCAA8B;AASvB,IAAM,WAAW,GAAjB,MAAM,WAAW;IA+CtB,OAAO;QACL,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;IACjE,CAAC;IAKD,SAAS;QACP,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;IACtC,CAAC;IAKD,cAAc;QACZ,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC;IAKD,MAAM,CAAC,QAAgB,EAAE;QACvB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAKD,MAAM;QACJ,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAKD,WAAW;QACT,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAKD,mBAAmB;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACrB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;QAC1D,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAKD,YAAY,CAAC,SAAiB,EAAE,SAAiB;QAC/C,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC;IACtE,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,SAAiB;QACtC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,EAAE,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7E,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACxD,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAKD,OAAO;QACL,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,WAAW,EAAE;YAClD,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;SACxC,CAAC;IACJ,CAAC;CACF,CAAA;AAjKY,kCAAW;AAEtB;IADC,IAAA,gCAAsB,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;uCAChC;AAGZ;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;2CACzB;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;0CACzD;AAQf;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;QACtC,OAAO,EAAE,QAAQ;QACjB,OAAO,EAAE,MAAM;KAChB,CAAC;;2CACwC;AAG1C;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;8CACzD;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;8CAC1C;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;+CACrD;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;8BAClC,IAAI;8CAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;8BAC/C,IAAI;mDAAC;AAGtB;IADC,IAAA,0BAAgB,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;8BAC1B,IAAI;8CAAC;AAGjB;IADC,IAAA,0BAAgB,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;8BAC1B,IAAI;8CAAC;AAKjB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,WAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC5C,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;8BACxB,WAAI;yCAAC;sBA1CD,WAAW;IAJvB,IAAA,gBAAM,EAAC,eAAe,CAAC;IACvB,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IAClC,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;GACR,WAAW,CAiKvB"}