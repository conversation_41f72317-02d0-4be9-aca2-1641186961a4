{"version": 3, "file": "initDatabase.js", "sourceRoot": "", "sources": ["../../src/scripts/initDatabase.ts"], "names": [], "mappings": ";;AA4KS,oCAAY;AAAE,sCAAa;AA5KpC,iDAAmD;AAKnD,KAAK,UAAU,YAAY;IACzB,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAG9B,IAAI,CAAC,wBAAa,CAAC,aAAa,EAAE,CAAC;YACjC,MAAM,wBAAa,CAAC,UAAU,EAAE,CAAC;QACnC,CAAC;QAGD,MAAM,gBAAgB,EAAE,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAE5B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACpC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,gBAAgB;IAC7B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAE7B,MAAM,YAAY,GAAG;QACnB;YACE,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,UAAU;YACrB,aAAa,EAAE,KAAK;YACpB,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,IAAI,CAAC;YACrE,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;YACnD,MAAM,EAAE,QAAQ;SACjB;QACD;YACE,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,UAAU;YACrB,aAAa,EAAE,KAAK;YACpB,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,IAAI,CAAC;YACpE,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE;YAClD,MAAM,EAAE,QAAQ;SACjB;QACD;YACE,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,UAAU;YACrB,aAAa,EAAE,KAAK;YACpB,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,IAAI,CAAC;YACpE,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE;YAClD,MAAM,EAAE,QAAQ;SACjB;QACD;YACE,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,aAAa,EAAE,GAAG;YAClB,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,CAAC;YACpE,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;YACnD,MAAM,EAAE,QAAQ;SACjB;QACD;YACE,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,aAAa,EAAE,GAAG;YAClB,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,GAAG,CAAC;YACrE,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;YACpD,MAAM,EAAE,QAAQ;SACjB;KACF,CAAC;IAEF,KAAK,MAAM,eAAe,IAAI,YAAY,EAAE,CAAC;QAE3C,MAAM,cAAc,GAAG,MAAM,wBAAa,CAAC,KAAK,CAC9C,6CAA6C,EAC7C,CAAC,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC;QAEF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAEhC,MAAM,wBAAa,CAAC,KAAK,CAAC;;;;;OAKzB,EAAE;gBACD,eAAe,CAAC,IAAI;gBACpB,eAAe,CAAC,IAAI;gBACpB,eAAe,CAAC,IAAI;gBACpB,eAAe,CAAC,SAAS,IAAI,IAAI;gBACjC,eAAe,CAAC,aAAa;gBAC7B,eAAe,CAAC,gBAAgB;gBAChC,eAAe,CAAC,gBAAgB;gBAChC,eAAe,CAAC,eAAe;gBAC/B,eAAe,CAAC,MAAM;aACvB,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,WAAW,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,cAAc,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;AACH,CAAC;AAOD,KAAK,UAAU,aAAa;IAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACvC,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAE7B,IAAI,CAAC;QAEH,MAAM,wBAAa,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAExD,MAAM,MAAM,GAAG;YACb,YAAY;YACZ,eAAe;YACf,kBAAkB;YAClB,aAAa;YACb,iBAAiB;YACjB,OAAO;YACP,eAAe;SAChB,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,wBAAa,CAAC,KAAK,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC;QACjC,CAAC;QAED,MAAM,wBAAa,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAExD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACnC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,YAAY,EAAE;SACX,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}