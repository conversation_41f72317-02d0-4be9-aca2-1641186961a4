"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initDatabase = initDatabase;
exports.clearDatabase = clearDatabase;
const database_1 = require("../config/database");
async function initDatabase() {
    try {
        console.log('🔄 开始初始化数据库...');
        if (!database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.initialize();
        }
        await initLotteryTypes();
        console.log('✅ 数据库初始化完成');
    }
    catch (error) {
        console.error('❌ 数据库初始化失败:', error);
        throw error;
    }
}
async function initLotteryTypes() {
    console.log('🔄 初始化彩种数据...');
    const lotteryTypes = [
        {
            code: 'FC3D',
            name: '福彩3D',
            type: '3star',
            draw_time: '21:15:00',
            draw_interval: 86400,
            collect_interval: parseInt(process.env.FC3D_COLLECT_INTERVAL || '30'),
            data_source_type: 'txt',
            data_source_url: process.env.FC3D_DATA_SOURCE || '',
            status: 'active'
        },
        {
            code: 'PL3',
            name: '排列三',
            type: '3star',
            draw_time: '21:30:00',
            draw_interval: 86400,
            collect_interval: parseInt(process.env.PL3_COLLECT_INTERVAL || '30'),
            data_source_type: 'txt',
            data_source_url: process.env.PL3_DATA_SOURCE || '',
            status: 'active'
        },
        {
            code: 'PL4',
            name: '排列四',
            type: '4star',
            draw_time: '21:30:00',
            draw_interval: 86400,
            collect_interval: parseInt(process.env.PL4_COLLECT_INTERVAL || '30'),
            data_source_type: 'txt',
            data_source_url: process.env.PL4_DATA_SOURCE || '',
            status: 'active'
        },
        {
            code: 'PC28',
            name: '加拿大PC28',
            type: '5star',
            draw_interval: 210,
            collect_interval: parseInt(process.env.PC28_COLLECT_INTERVAL || '5'),
            data_source_type: 'api',
            data_source_url: process.env.PC28_DATA_SOURCE || '',
            status: 'active'
        },
        {
            code: 'XYWXC',
            name: '幸运五星彩前四',
            type: '4star',
            draw_interval: 300,
            collect_interval: parseInt(process.env.XYWXC_COLLECT_INTERVAL || '5'),
            data_source_type: 'api',
            data_source_url: process.env.XYWXC_DATA_SOURCE || '',
            status: 'active'
        }
    ];
    for (const lotteryTypeData of lotteryTypes) {
        const existingResult = await database_1.AppDataSource.query('SELECT id FROM lottery_types WHERE code = ?', [lotteryTypeData.code]);
        if (existingResult.length === 0) {
            await database_1.AppDataSource.query(`
        INSERT INTO lottery_types (
          code, name, type, draw_time, draw_interval, collect_interval,
          data_source_type, data_source_url, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
                lotteryTypeData.code,
                lotteryTypeData.name,
                lotteryTypeData.type,
                lotteryTypeData.draw_time || null,
                lotteryTypeData.draw_interval,
                lotteryTypeData.collect_interval,
                lotteryTypeData.data_source_type,
                lotteryTypeData.data_source_url,
                lotteryTypeData.status
            ]);
            console.log(`✅ 创建彩种: ${lotteryTypeData.name}`);
        }
        else {
            console.log(`⚠️  彩种已存在: ${lotteryTypeData.name}`);
        }
    }
}
async function clearDatabase() {
    if (process.env.NODE_ENV !== 'development') {
        throw new Error('清理数据库操作只能在开发环境中执行');
    }
    console.log('🔄 清理数据库数据...');
    try {
        await database_1.AppDataSource.query('SET FOREIGN_KEY_CHECKS = 0');
        const tables = [
            'admin_logs',
            'user_sessions',
            'user_memberships',
            'predictions',
            'lottery_results',
            'users',
            'lottery_types'
        ];
        for (const table of tables) {
            await database_1.AppDataSource.query(`TRUNCATE TABLE ${table}`);
            console.log(`✅ 清理表: ${table}`);
        }
        await database_1.AppDataSource.query('SET FOREIGN_KEY_CHECKS = 1');
        console.log('✅ 数据库清理完成');
    }
    catch (error) {
        console.error('❌ 数据库清理失败:', error);
        throw error;
    }
}
if (require.main === module) {
    initDatabase()
        .then(() => {
        console.log('🎉 数据库初始化成功完成');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 数据库初始化失败:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=initDatabase.js.map