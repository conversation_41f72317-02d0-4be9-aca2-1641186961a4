import { AppDataSource } from '../config/database';
import axios from 'axios';

/**
 * 数据采集服务
 * 负责从不同数据源采集彩票开奖数据
 */
export class DataCollectionService {
  private static instance: DataCollectionService;
  private isRunning: boolean = false;
  private intervals: Map<string, NodeJS.Timeout> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): DataCollectionService {
    if (!DataCollectionService.instance) {
      DataCollectionService.instance = new DataCollectionService();
    }
    return DataCollectionService.instance;
  }

  /**
   * 启动数据采集服务
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️  数据采集服务已在运行中');
      return;
    }

    console.log('🔄 启动数据采集服务...');
    this.isRunning = true;

    try {
      // 获取所有启用的彩种
      const lotteryTypes = await this.getActiveLotteryTypes();
      
      // 为每个彩种启动采集任务
      for (const lotteryType of lotteryTypes) {
        await this.startCollectionForLotteryType(lotteryType);
      }

      console.log('✅ 数据采集服务启动成功');
    } catch (error) {
      console.error('❌ 数据采集服务启动失败:', error);
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * 停止数据采集服务
   */
  stop(): void {
    console.log('🔄 停止数据采集服务...');
    
    // 清除所有定时器
    this.intervals.forEach((interval, lotteryCode) => {
      clearInterval(interval);
      console.log(`⏹️  停止 ${lotteryCode} 数据采集`);
    });
    
    this.intervals.clear();
    this.isRunning = false;
    console.log('✅ 数据采集服务已停止');
  }

  /**
   * 获取所有启用的彩种
   */
  private async getActiveLotteryTypes(): Promise<any[]> {
    const result = await AppDataSource.query(`
      SELECT id, code, name, type, draw_time, draw_interval, collect_interval,
             data_source_type, data_source_url, status
      FROM lottery_types 
      WHERE status = 'active'
    `);
    return result;
  }

  /**
   * 为指定彩种启动采集任务
   */
  private async startCollectionForLotteryType(lotteryType: any): Promise<void> {
    const { code, name, collect_interval, data_source_type, data_source_url } = lotteryType;

    if (!data_source_url) {
      console.log(`⚠️  ${name} 未配置数据源，跳过采集`);
      return;
    }

    console.log(`🚀 启动 ${name} 数据采集，间隔: ${collect_interval}秒`);

    // 立即检查是否需要采集
    if (await this.shouldCollectNow(lotteryType)) {
      await this.collectDataForLotteryType(lotteryType);
    }

    // 设置定时采集
    const interval = setInterval(async () => {
      try {
        // 检查是否在采集时间窗口内
        if (await this.shouldCollectNow(lotteryType)) {
          await this.collectDataForLotteryType(lotteryType);
        } else {
          console.log(`⏰ ${name} 当前不在采集时间窗口内，跳过采集`);
        }
      } catch (error) {
        console.error(`❌ ${name} 数据采集失败:`, error);
      }
    }, collect_interval * 1000);

    this.intervals.set(code, interval);
  }

  /**
   * 为指定彩种采集数据
   */
  private async collectDataForLotteryType(lotteryType: any): Promise<void> {
    const { code, name, data_source_type, data_source_url } = lotteryType;

    console.log(`🔄 开始采集 ${name} 数据，数据源: ${data_source_url}`);

    try {
      let newData: any[] = [];

      if (data_source_type === 'txt') {
        console.log(`📄 从TXT文件采集 ${name} 数据...`);
        newData = await this.collectFromTxtFile(data_source_url, code);
      } else if (data_source_type === 'api') {
        console.log(`🔗 从API接口采集 ${name} 数据...`);
        newData = await this.collectFromApi(data_source_url, code);
      } else {
        console.log(`⚠️  ${name} 不支持的数据源类型: ${data_source_type}`);
        return;
      }

      console.log(`📊 ${name} 解析到 ${newData.length} 条数据`);

      if (newData.length > 0) {
        await this.saveCollectedData(lotteryType, newData);
        console.log(`✅ ${name} 采集到 ${newData.length} 条新数据`);
      } else {
        console.log(`ℹ️  ${name} 暂无新数据`);
      }

    } catch (error) {
      console.error(`❌ ${name} 数据采集失败:`, error);
      throw error;
    }
  }

  /**
   * 从TXT文件采集数据
   */
  private async collectFromTxtFile(url: string, lotteryCode?: string): Promise<any[]> {
    try {
      console.log(`🌐 请求URL: ${url}`);
      const response = await axios.get(url, {
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      console.log(`📥 响应状态: ${response.status}, 数据长度: ${response.data.length}`);
      const content = response.data;
      const parsedData = this.parseTxtContent(content, lotteryCode);
      console.log(`🔍 解析结果: ${parsedData.length} 条记录`);
      return parsedData;
    } catch (error) {
      console.error('TXT文件采集失败:', error);
      return [];
    }
  }

  /**
   * 从API接口采集数据
   */
  private async collectFromApi(url: string, lotteryCode?: string): Promise<any[]> {
    try {
      // 为PC28添加时间戳防止CDN缓存
      let requestUrl = url;
      if (lotteryCode === 'PC28') {
        const timestamp = Date.now();
        requestUrl = `${url}&V=${timestamp}`;
      }

      console.log(`🌐 请求API: ${requestUrl}`);
      const response = await axios.get(requestUrl, {
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      console.log(`📥 API响应状态: ${response.status}`);
      const parsedData = this.parseApiResponse(response.data, lotteryCode);
      console.log(`🔍 API解析结果: ${parsedData.length} 条记录`);
      return parsedData;
    } catch (error) {
      console.error('API接口采集失败:', error);
      return [];
    }
  }

  /**
   * 解析TXT文件内容
   */
  private parseTxtContent(content: string, lotteryCode?: string): any[] {
    const lines = content.split('\n').filter(line => line.trim());
    const results: any[] = [];

    for (const line of lines) {
      const parsed = this.parseTxtLine(line.trim(), lotteryCode);
      if (parsed) {
        results.push(parsed);
      }
    }

    return results;
  }

  /**
   * 解析TXT文件单行数据
   * 支持两种格式：
   * 1. 排列五格式: 2004001 2004-11-14 9 2 8 8 2 271814 1 100000
   * 2. 福彩3D格式: 2002001 2002-01-01 0 7 3 5 2 6 2 2 0 0 0 0 0 0 0
   */
  private parseTxtLine(line: string, lotteryCode?: string): any | null {
    try {
      const parts = line.split(/\s+/);
      if (parts.length < 5) {
        return null;
      }

      const period = parts[0];
      const date = parts[1];
      const num1 = parts[2];
      const num2 = parts[3];
      const num3 = parts[4];

      // 根据彩种类型确定开奖时间和号码格式
      if (lotteryCode === 'FC3D') {
        // 福彩3D格式: 期号 日期 第一位 第二位 第三位 ...
        return {
          period,
          drawTime: `${date} 21:15:00`,
          fc3dNumbers: `${num1}${num2}${num3}`  // 福彩3D：前3位
        };
      } else {
        // 排列五格式: 期号 日期 第一位 第二位 第三位 第四位 第五位 ...
        if (parts.length < 7) {
          return null;
        }

        const num4 = parts[5];
        const num5 = parts[6];

        return {
          period,
          drawTime: `${date} 21:30:00`,
          pl3Numbers: `${num1}${num2}${num3}`,  // 排列三：前3位
          pl4Numbers: `${num1}${num2}${num3}${num4}`,  // 排列四：前4位
          pl5Numbers: `${num1}${num2}${num3}${num4}${num5}`  // 排列五：全部5位
        };
      }
    } catch (error) {
      console.error('解析TXT行失败:', line, error);
      return null;
    }
  }

  /**
   * 解析API响应数据
   */
  private parseApiResponse(data: any, lotteryCode?: string): any[] {
    try {
      if (lotteryCode === 'PC28') {
        return this.parsePC28ApiResponse(data);
      }

      // 其他彩种的API解析逻辑
      console.log('未知彩种API响应数据:', data);
      return [];
    } catch (error) {
      console.error('解析API响应失败:', error);
      return [];
    }
  }

  /**
   * 解析PC28 API响应数据
   */
  private parsePC28ApiResponse(data: any): any[] {
    try {
      // PC28实时接口返回格式: {cur: {...}, pre: {...}, serverTime: ...}
      if (data.pre && data.pre.turnNum && data.pre.openNum && data.pre.openTime) {
        const { turnNum, openNum, openTime } = data.pre;

        // 将开奖时间转换为标准格式
        const drawTime = new Date(openTime).toISOString().replace('T', ' ').substring(0, 19);

        return [{
          period: turnNum,
          drawTime: drawTime,
          pc28Numbers: openNum // 格式如 "6,1,1"
        }];
      }

      return [];
    } catch (error) {
      console.error('解析PC28 API响应失败:', error);
      return [];
    }
  }

  /**
   * 保存采集到的数据
   */
  private async saveCollectedData(lotteryType: any, data: any[]): Promise<void> {
    const { id: lotteryTypeId, code } = lotteryType;

    for (const item of data) {
      try {
        let numbers = '';

        // 根据彩种类型选择对应的号码
        if (code === 'FC3D') {
          numbers = item.fc3dNumbers;
        } else if (code === 'PL3') {
          numbers = item.pl3Numbers;
        } else if (code === 'PL4') {
          numbers = item.pl4Numbers;
        } else if (code === 'PL5') {
          numbers = item.pl5Numbers;
        } else if (code === 'PC28') {
          numbers = item.pc28Numbers;
        } else {
          // 其他彩种使用原来的逻辑
          numbers = item.numbers || '';
        }

        if (!numbers) {
          continue;
        }

        // 检查是否已存在
        const existing = await AppDataSource.query(
          'SELECT id FROM lottery_results WHERE lottery_type_id = ? AND period = ?',
          [lotteryTypeId, item.period]
        );

        if (existing.length === 0) {
          // 插入新记录
          await AppDataSource.query(`
            INSERT INTO lottery_results (lottery_type_id, period, draw_time, numbers)
            VALUES (?, ?, ?, ?)
          `, [lotteryTypeId, item.period, item.drawTime, numbers]);
        }
      } catch (error) {
        console.error('保存数据失败:', item, error);
      }
    }
  }

  /**
   * 手动触发指定彩种的数据采集
   */
  async manualCollect(lotteryCode: string): Promise<void> {
    const lotteryTypes = await this.getActiveLotteryTypes();
    const lotteryType = lotteryTypes.find(lt => lt.code === lotteryCode);

    if (!lotteryType) {
      throw new Error(`彩种 ${lotteryCode} 不存在或未启用`);
    }

    // 对于PC28，先检查历史数据是否充足
    if (lotteryCode === 'PC28') {
      await this.ensurePC28HistoricalData(lotteryType);
    }

    await this.collectDataForLotteryType(lotteryType);
  }

  /**
   * 确保PC28有足够的历史数据（至少2000条）
   */
  private async ensurePC28HistoricalData(lotteryType: any): Promise<void> {
    const { id: lotteryTypeId } = lotteryType;

    // 检查当前数据量
    const countResult = await AppDataSource.query(`
      SELECT COUNT(*) as count FROM lottery_results WHERE lottery_type_id = ?
    `, [lotteryTypeId]);

    const currentCount = countResult[0].count;
    console.log(`📊 PC28当前数据量: ${currentCount} 条`);

    if (currentCount >= 2000) {
      console.log(`✅ PC28历史数据充足，无需补充`);
      return;
    }

    console.log(`🔄 PC28历史数据不足，开始批量采集历史数据...`);
    await this.batchCollectPC28HistoricalData(lotteryType);
  }

  /**
   * 批量采集PC28历史数据（优化版）
   */
  private async batchCollectPC28HistoricalData(lotteryType: any): Promise<void> {
    const today = new Date();
    const allHistoricalData: any[] = [];

    // 从5天前开始采集到当天
    console.log(`📅 开始批量采集PC28历史数据（5天前到当天）...`);

    for (let i = 5; i >= 0; i--) { // 从5天前到当天
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD格式

      console.log(`📅 采集 ${dateStr} 的PC28历史数据...`);
      const dayData = await this.fetchPC28HistoricalDataForDate(lotteryType, dateStr!, false); // 不保存，只返回数据

      if (dayData.length > 0) {
        allHistoricalData.push(...dayData);
        console.log(`✅ ${dateStr} 采集到 ${dayData.length} 条数据`);
      } else {
        console.log(`ℹ️  ${dateStr} 暂无数据`);
      }
    }

    if (allHistoricalData.length === 0) {
      console.log(`⚠️  未采集到任何历史数据`);
      return;
    }

    console.log(`📊 总计采集到 ${allHistoricalData.length} 条原始数据，开始去重...`);

    // 按期号去重
    const uniqueData = this.deduplicateByPeriod(allHistoricalData);
    console.log(`🔍 去重后剩余 ${uniqueData.length} 条数据`);

    // 批量写入数据库
    if (uniqueData.length > 0) {
      await this.batchSaveHistoricalData(lotteryType, uniqueData);
      console.log(`✅ PC28历史数据批量写入完成: ${uniqueData.length} 条`);
    }
  }

  /**
   * 获取PC28指定日期的历史数据
   */
  private async fetchPC28HistoricalDataForDate(lotteryType: any, date: string, saveToDb: boolean = true): Promise<any[]> {
    try {
      const url = `https://23.248.233.80/anls-api/data/capc28/lotteryList/${date}.do`;
      console.log(`🌐 请求PC28历史数据: ${url}`);

      const response = await axios.get(url, {
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const historicalData = this.parsePC28HistoricalData(response.data);

      if (saveToDb && historicalData.length > 0) {
        await this.saveCollectedData(lotteryType, historicalData);
        console.log(`✅ ${date} PC28历史数据保存成功: ${historicalData.length} 条`);
      }

      return historicalData;

    } catch (error) {
      console.error(`❌ 获取 ${date} PC28历史数据失败:`, error);
      return [];
    }
  }

  /**
   * 解析PC28历史数据
   */
  private parsePC28HistoricalData(data: any[]): any[] {
    try {
      if (!Array.isArray(data)) {
        return [];
      }

      return data.map(item => ({
        period: item.issue,
        drawTime: item.openDateTime,
        pc28Numbers: item.openNum.join(',') // 将数组转换为逗号分隔的字符串
      })).filter(item => item.period && item.drawTime && item.pc28Numbers);

    } catch (error) {
      console.error('解析PC28历史数据失败:', error);
      return [];
    }
  }

  /**
   * 按期号去重
   */
  private deduplicateByPeriod(data: any[]): any[] {
    const periodMap = new Map();

    // 按期号去重，保留最新的数据
    data.forEach(item => {
      if (item.period) {
        periodMap.set(item.period, item);
      }
    });

    return Array.from(periodMap.values());
  }

  /**
   * 批量保存历史数据
   */
  private async batchSaveHistoricalData(lotteryType: any, data: any[]): Promise<void> {
    const { id: lotteryTypeId } = lotteryType;

    if (data.length === 0) {
      return;
    }

    // 构建批量插入SQL
    const values = data.map(item => {
      const numbers = item.pc28Numbers || '';
      return `(${lotteryTypeId}, '${item.period}', '${item.drawTime}', '${numbers}')`;
    }).join(', ');

    const sql = `
      INSERT IGNORE INTO lottery_results (lottery_type_id, period, draw_time, numbers)
      VALUES ${values}
    `;

    try {
      const result = await AppDataSource.query(sql);
      console.log(`📊 批量插入完成，影响行数: ${result.affectedRows}`);
    } catch (error) {
      console.error('批量保存历史数据失败:', error);
      throw error;
    }
  }

  /**
   * 初始化彩种数据（管理后台调用）
   */
  async initializeLotteryData(lotteryCode: string): Promise<void> {
    const lotteryTypes = await this.getActiveLotteryTypes();
    const lotteryType = lotteryTypes.find(lt => lt.code === lotteryCode);

    if (!lotteryType) {
      throw new Error(`彩种 ${lotteryCode} 不存在或未启用`);
    }

    console.log(`🔄 开始初始化 ${lotteryType.name} 数据...`);

    if (lotteryCode === 'PC28') {
      await this.batchCollectPC28HistoricalData(lotteryType);
    } else {
      // 其他彩种的初始化逻辑
      await this.collectDataForLotteryType(lotteryType);
    }

    console.log(`✅ ${lotteryType.name} 数据初始化完成`);
  }

  /**
   * 判断当前是否应该采集数据
   */
  private async shouldCollectNow(lotteryType: any): Promise<boolean> {
    const { id, code, draw_time, draw_interval } = lotteryType;
    const now = new Date();

    // 高频彩（间隔小于1天）始终采集
    if (draw_interval && draw_interval < 86400) {
      return true;
    }

    // 传统彩（排列三、排列四等）只在开奖时间后30分钟内采集
    if (draw_time) {
      const today = new Date();
      const [hours, minutes, seconds] = draw_time.split(':').map(Number);

      // 构建今天的开奖时间
      const drawTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, seconds);

      // 构建采集窗口结束时间（开奖后30分钟）
      const collectEndTime = new Date(drawTime.getTime() + 30 * 60 * 1000);

      // 检查当前时间是否在开奖时间到开奖后30分钟之间
      const inTimeWindow = now >= drawTime && now <= collectEndTime;

      if (!inTimeWindow) {
        console.log(`⏰ ${code} 当前时间 ${now.toLocaleTimeString()} 不在采集窗口内 (${drawTime.toLocaleTimeString()} - ${collectEndTime.toLocaleTimeString()})`);
        return false;
      }

      // 检查是否已经采集到今天的数据
      const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD格式
      const existingData = await AppDataSource.query(`
        SELECT COUNT(*) as count
        FROM lottery_results
        WHERE lottery_type_id = ? AND DATE(draw_time) = ?
      `, [id, todayStr]);

      const hasToday = existingData[0].count > 0;

      if (hasToday) {
        console.log(`✅ ${code} 今日数据已采集，跳过重复采集`);
        return false;
      }

      console.log(`🎯 ${code} 在采集窗口内且未采集今日数据，开始采集`);
      return true;
    }

    // 默认采集
    return true;
  }

  /**
   * 更新彩种采集配置
   */
  async updateLotteryConfig(lotteryCode: string, config: {
    collectInterval?: number;
    dataSourceUrl?: string;
    collectWindowMinutes?: number;
  }): Promise<void> {
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (config.collectInterval !== undefined) {
      updateFields.push('collect_interval = ?');
      updateValues.push(config.collectInterval);
    }

    if (config.dataSourceUrl !== undefined) {
      updateFields.push('data_source_url = ?');
      updateValues.push(config.dataSourceUrl);
    }

    if (updateFields.length > 0) {
      updateValues.push(lotteryCode);

      await AppDataSource.query(`
        UPDATE lottery_types
        SET ${updateFields.join(', ')}, updated_at = NOW()
        WHERE code = ?
      `, updateValues);

      console.log(`✅ 更新 ${lotteryCode} 采集配置成功`);

      // 如果服务正在运行，重启该彩种的采集任务
      if (this.isRunning && this.intervals.has(lotteryCode)) {
        await this.restartCollectionForLottery(lotteryCode);
      }
    }
  }

  /**
   * 重启指定彩种的采集任务
   */
  private async restartCollectionForLottery(lotteryCode: string): Promise<void> {
    // 停止当前采集任务
    const interval = this.intervals.get(lotteryCode);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(lotteryCode);
      console.log(`⏹️  停止 ${lotteryCode} 采集任务`);
    }

    // 获取最新配置并重新启动
    const lotteryTypes = await this.getActiveLotteryTypes();
    const lotteryType = lotteryTypes.find(lt => lt.code === lotteryCode);

    if (lotteryType) {
      await this.startCollectionForLotteryType(lotteryType);
      console.log(`🔄 重启 ${lotteryCode} 采集任务`);
    }
  }

  /**
   * 获取采集状态
   */
  getStatus(): any {
    return {
      isRunning: this.isRunning,
      activeCollectors: Array.from(this.intervals.keys()),
      totalCollectors: this.intervals.size
    };
  }
}
